# -*- coding: utf-8 -*-
import cairo
import numpy as np
from PIL import Image
import math


def create_seasonal_kaleidoscope():
    """创建四季万花筒 - 第三种分形树万花筒变体"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 渐变背景
    bg_gradient = cairo.RadialGradient(width//2, height//2, 0, width//2, height//2, width//2)
    bg_gradient.add_color_stop_rgba(0, 0.95, 0.95, 0.98, 1)
    bg_gradient.add_color_stop_rgba(1, 0.3, 0.4, 0.6, 1)
    ctx.set_source(bg_gradient)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2
    
    # 四个季节的色彩方案
    seasons = [
        {"name": "春", "hue_base": 100, "colors": [(0.4, 0.8, 0.3), (0.9, 0.9, 0.4), (0.6, 0.9, 0.6)]},  # 绿色系
        {"name": "夏", "hue_base": 60,  "colors": [(0.2, 0.7, 0.2), (0.8, 0.9, 0.2), (0.4, 0.8, 0.4)]},   # 深绿系
        {"name": "秋", "hue_base": 30,  "colors": [(0.9, 0.6, 0.2), (0.9, 0.4, 0.1), (0.8, 0.7, 0.3)]},   # 橙红系
        {"name": "冬", "hue_base": 240, "colors": [(0.6, 0.7, 0.9), (0.4, 0.5, 0.8), (0.8, 0.8, 0.9)]},   # 蓝白系
    ]
    
    def draw_seasonal_branch(start_x, start_y, length, angle, depth, season):
        """绘制季节性分形树枝"""
        if depth <= 0 or length < 2:
            return
        
        end_x = start_x + length * math.cos(angle)
        end_y = start_y + length * math.sin(angle)
        
        # 选择季节颜色
        color_idx = depth % len(season["colors"])
        r, g, b = season["colors"][color_idx]
        
        # 根据深度调整颜色强度
        intensity = max(0.3, 1 - depth * 0.1)
        line_width = max(0.5, depth * 1.0)
        
        ctx.set_source_rgba(r * intensity, g * intensity, b * intensity, 0.8)
        ctx.set_line_width(line_width)
        ctx.move_to(start_x, start_y)
        ctx.line_to(end_x, end_y)
        ctx.stroke()
        
        # 添加季节特效
        if depth <= 3:
            # 春夏：花朵/叶子效果
            if season["name"] in ["春", "夏"]:
                leaf_gradient = cairo.RadialGradient(end_x, end_y, 0, end_x, end_y, line_width * 2)
                leaf_gradient.add_color_stop_rgba(0, r, g, b, 0.6)
                leaf_gradient.add_color_stop_rgba(1, r * 0.5, g * 0.5, b * 0.5, 0)
                
                ctx.set_source(leaf_gradient)
                ctx.arc(end_x, end_y, line_width * 2, 0, 2 * math.pi)
                ctx.fill()
            
            # 秋：飘落的叶子
            elif season["name"] == "秋":
                for i in range(3):
                    leaf_x = end_x + (i - 1) * 5
                    leaf_y = end_y + i * 3
                    ctx.set_source_rgba(0.9, 0.7 - i * 0.1, 0.2, 0.7)
                    ctx.arc(leaf_x, leaf_y, 2, 0, 2 * math.pi)
                    ctx.fill()
            
            # 冬：雪花效果
            elif season["name"] == "冬":
                ctx.set_source_rgba(0.9, 0.9, 1, 0.8)
                ctx.arc(end_x, end_y, line_width, 0, 2 * math.pi)
                ctx.fill()
        
        # 递归分支
        new_length = length * 0.72
        draw_seasonal_branch(end_x, end_y, new_length, angle - math.pi/7, depth - 1, season)
        draw_seasonal_branch(end_x, end_y, new_length, angle + math.pi/7, depth - 1, season)
    
    # 绘制四象限季节树
    for i, season in enumerate(seasons):
        # 每个季节占据一个象限
        quadrant_angle = i * math.pi / 2
        
        # 在象限内绘制多棵树
        for tree_idx in range(12):
            tree_angle = quadrant_angle + (tree_idx - 3.5) * math.pi / 16
            tree_distance = 80 + tree_idx * 25
            
            tree_x = center_x + tree_distance * math.cos(tree_angle)
            tree_y = center_y + tree_distance * math.sin(tree_angle)
            
            # 树的生长方向指向中心
            growth_angle = tree_angle + math.pi - math.pi / 8 + (tree_idx % 3 - 1) * math.pi / 12
            initial_length = max(20, 35 - tree_idx * 2)
            
            draw_seasonal_branch(tree_x, tree_y, initial_length, growth_angle, 6, season)
    
    # 中央四季交汇点
    center_gradient = cairo.RadialGradient(center_x, center_y, 0, center_x, center_y, 40)
    center_gradient.add_color_stop_rgba(0, 1, 1, 1, 1)
    center_gradient.add_color_stop_rgba(0.25, 0.4, 0.8, 0.3, 0.8)  # 春
    center_gradient.add_color_stop_rgba(0.5, 0.2, 0.7, 0.2, 0.8)   # 夏
    center_gradient.add_color_stop_rgba(0.75, 0.9, 0.6, 0.2, 0.8)  # 秋
    center_gradient.add_color_stop_rgba(1, 0.6, 0.7, 0.9, 0.8)     # 冬
    
    ctx.set_source(center_gradient)
    ctx.arc(center_x, center_y, 40, 0, 2 * math.pi)
    ctx.fill()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


if __name__ == "__main__":
    seasonal_kaleidoscope = create_seasonal_kaleidoscope()
    seasonal_kaleidoscope.save("seasonal_kaleidoscope.png")
    seasonal_kaleidoscope.show()
    
