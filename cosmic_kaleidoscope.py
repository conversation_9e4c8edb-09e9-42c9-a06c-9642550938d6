# -*- coding: utf-8 -*-
import cairo
import numpy as np
from PIL import Image
import math


def create_galactic_mandala():
    """创建星系曼陀罗 - 另一种万花筒与星系的融合方式"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)

    # 渐变背景 - 从深蓝到黑色
    bg_gradient = cairo.RadialGradient(width//2, height//2, 0, width//2, height//2, width//2)
    bg_gradient.add_color_stop_rgba(0, 0.1, 0.1, 0.3, 1)
    bg_gradient.add_color_stop_rgba(0.7, 0.05, 0.05, 0.2, 1)
    bg_gradient.add_color_stop_rgba(1, 0.02, 0.02, 0.08, 1)
    ctx.set_source(bg_gradient)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2

     # 中央核心
    core_gradient = cairo.RadialGradient(center_x, center_y, 0, center_x, center_y, 40)
    core_gradient.add_color_stop_rgba(0, 1, 1, 0.8, 1)
    core_gradient.add_color_stop_rgba(0.5, 1, 0.6, 0.2, 0.9)
    core_gradient.add_color_stop_rgba(1, 0.6, 0.2, 0, 0.6)
    
    ctx.set_source(core_gradient)
    ctx.arc(center_x, center_y, 40, 0, 2 * math.pi)
    ctx.fill()    

    # 创建多层旋转的星系结构
    for layer in range(6):
        layer_radius = 60 + layer * 50
        layer_rotation = layer * math.pi / 12  # 每层稍微旋转
        
        ctx.save()
        ctx.translate(center_x, center_y)
        ctx.rotate(layer_rotation)
        
        # 每层有不同数量的螺旋臂
        arms_count = 3 + layer
        
        for arm in range(arms_count):
            arm_angle = arm * 2 * math.pi / arms_count
            
            # 绘制螺旋臂
            for i in range(100):
                t = i * 0.03
                spiral_radius = layer_radius * 0.3 + t * layer_radius * 0.8
                spiral_angle = t * 2 + arm_angle
                
                if spiral_radius <= layer_radius:
                    x = spiral_radius * math.cos(spiral_angle)
                    y = spiral_radius * math.sin(spiral_angle)
                    
                    # 万花筒色彩系统
                    color_phase = (layer * 60 + arm * 45 + t * 180) % 360
                    intensity = max(0.3, 1 - t * 0.8)
                    
                    r = intensity * (0.4 + 0.6 * math.sin(math.radians(color_phase)))
                    g = intensity * (0.4 + 0.6 * math.sin(math.radians(color_phase + 120)))
                    b = intensity * (0.4 + 0.6 * math.sin(math.radians(color_phase + 240)))
                    
                    # 星点大小
                    star_size = max(1, 4 - t * 2) * (1 + layer * 0.2)
                    
                    # 绘制多彩星点
                    star_gradient = cairo.RadialGradient(x, y, 0, x, y, star_size * 2)
                    star_gradient.add_color_stop_rgba(0, r, g, b, intensity)
                    star_gradient.add_color_stop_rgba(0.6, r * 0.7, g * 0.7, b * 0.7, intensity * 0.7)
                    star_gradient.add_color_stop_rgba(1, r * 0.3, g * 0.3, b * 0.3, 0)
                    
                    ctx.set_source(star_gradient)
                    ctx.arc(x, y, star_size * 2, 0, 2 * math.pi)
                    ctx.fill()
        
        ctx.restore()
    
    # 添加万花筒式的径向装饰
    for radius in range(100, 350, 30):
        decorations = int(radius / 15)  # 装饰数量随半径增加
        
        for i in range(decorations):
            angle = i * 2 * math.pi / decorations
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            
            # 万花筒装饰色彩
            deco_hue = (radius + i * 36) % 360
            dr = 0.6 + 0.4 * math.sin(math.radians(deco_hue))
            dg = 0.6 + 0.4 * math.sin(math.radians(deco_hue + 120))
            db = 0.6 + 0.4 * math.sin(math.radians(deco_hue + 240))
            
            deco_size = max(3, 8 - radius * 0.01)
            
            deco_gradient = cairo.RadialGradient(x, y, 0, x, y, deco_size)
            deco_gradient.add_color_stop_rgba(0, dr, dg, db, 0.7)
            deco_gradient.add_color_stop_rgba(1, dr * 0.4, dg * 0.4, db * 0.4, 0)
        
            ctx.set_source(deco_gradient)
            ctx.arc(x, y, deco_size, 0, 2 * math.pi)
            ctx.fill()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


if __name__ == "__main__":
    galactic_mandala = create_galactic_mandala()
    galactic_mandala.save("galactic_mandala.png")
    galactic_mandala.show()

    
