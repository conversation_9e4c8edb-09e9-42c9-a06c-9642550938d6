NVCC_GENCODE is -gencode=arch=compute_50,code=sm_50 -gencode=arch=compute_60,code=sm_60 -gencode=arch=compute_61,code=sm_61 -gencode=arch=compute_70,code=sm_70 -gencode=arch=compute_80,code=sm_80 -gencode=arch=compute_90,code=sm_90 -gencode=arch=compute_100,code=sm_100 -gencode=arch=compute_120,code=sm_120 -gencode=arch=compute_120,code=compute_120
编译 NCCL SendRecv MPI 测试程序...
编译完成: /home/<USER>/nccl-2.27.3/test/build/sendrecv_mpi_test
运行本地测试（2个进程）...
强制使用网络传输（禁用P2P和共享内存）...
进程 1: NCCL 版本 22703
进程 0: NCCL 版本 22703
进程 0: 使用GPU设备 0
ubuntu:1038245:1038245 [0] NCCL INFO NCCL_SOCKET_IFNAME set to lo
ubuntu:1038245:1038245 [0] NCCL INFO cudaDriverVersion 12080
ubuntu:1038245:1038245 [0] NCCL INFO NCCL version 2.27.3+cuda12.8
进程 1: 使用GPU设备 1
ubuntu:1038246:1038246 [1] NCCL INFO cudaDriverVersion 12080
ubuntu:1038246:1038246 [1] NCCL INFO NCCL_SOCKET_IFNAME set to lo
ubuntu:1038246:1038246 [1] NCCL INFO NCCL version 2.27.3+cuda12.8
ubuntu:1038245:1038245 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. 
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB: [0] rocep193s0f0:uverbs0:1/RoCE provider=Mlx5 speed=100000 context=0x5c3d62576100 pciPath=/sys/devices/pci0000:c0/0000:c0:01.1/0000:c1:00.0 ar=0
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB : Made virtual device [0] name=rocep193s0f0 speed=100000 ndevs=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB: [1] rocep193s0f1:uverbs1:1/RoCE provider=Mlx5 speed=100000 context=0x5c3d625b77b0 pciPath=/sys/devices/pci0000:c0/0000:c0:01.1/0000:c1:00.0 ar=0
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB : Made virtual device [1] name=rocep193s0f1 speed=100000 ndevs=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB: [2] ibp196s0:uverbs2:1/IB provider=Mlx5 speed=100000 context=0x5c3d625f8e60 pciPath=/sys/devices/pci0000:c0/0000:c0:03.1/0000:c2:00.0/0000:c3:04.0/0000:c4:00.0 ar=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB : Made virtual device [2] name=ibp196s0 speed=100000 ndevs=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB: [3] ibp131s0:uverbs3:1/IB provider=Mlx5 speed=100000 context=0x5c3d6263a510 pciPath=/sys/devices/pci0000:80/0000:80:01.1/0000:81:00.0/0000:82:04.0/0000:83:00.0 ar=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB : Made virtual device [3] name=ibp131s0 speed=100000 ndevs=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB: [4] ibp136s0:uverbs4:1/IB provider=Mlx5 speed=100000 context=0x5c3d6267bbc0 pciPath=/sys/devices/pci0000:80/0000:80:03.1/0000:86:00.0/0000:87:04.0/0000:88:00.0 ar=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB : Made virtual device [4] name=ibp136s0 speed=100000 ndevs=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. 
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB: [5] ibp3s0:uverbs5:1/IB provider=Mlx5 speed=100000 context=0x5c3d626bd270 pciPath=/sys/devices/pci0000:00/0000:00:01.1/0000:01:00.0/0000:02:04.0/0000:03:00.0 ar=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB : Made virtual device [5] name=ibp3s0 speed=100000 ndevs=1
ubuntu:1038245:1038245 [0] NCCL INFO NET/IB : Using [0]rocep193s0f0:1/RoCE [1]rocep193s0f1:1/RoCE [2]ibp196s0:1/IB [3]ibp131s0:1/IB [4]ibp136s0:1/IB [5]ibp3s0:1/IB [RO]; OOB lo:127.0.0.1<0>
ubuntu:1038245:1038245 [0] NCCL INFO Initialized NET plugin IB
ubuntu:1038245:1038245 [0] NCCL INFO NET/Socket : Using [0]lo:127.0.0.1<0>
ubuntu:1038245:1038245 [0] NCCL INFO Initialized NET plugin Socket
ubuntu:1038245:1038245 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038245:1038245 [0] NCCL INFO Assigned NET plugin Socket to comm
ubuntu:1038245:1038245 [0] NCCL INFO Using network Socket
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB: [0] rocep193s0f0:uverbs0:1/RoCE provider=Mlx5 speed=100000 context=0x5786f317ecc0 pciPath=/sys/devices/pci0000:c0/0000:c0:01.1/0000:c1:00.0 ar=0
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB : Made virtual device [0] name=rocep193s0f0 speed=100000 ndevs=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB: [1] rocep193s0f1:uverbs1:1/RoCE provider=Mlx5 speed=100000 context=0x5786f31c0370 pciPath=/sys/devices/pci0000:c0/0000:c0:01.1/0000:c1:00.0 ar=0
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB : Made virtual device [1] name=rocep193s0f1 speed=100000 ndevs=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB: [2] ibp196s0:uverbs2:1/IB provider=Mlx5 speed=100000 context=0x5786f3201a20 pciPath=/sys/devices/pci0000:c0/0000:c0:03.1/0000:c2:00.0/0000:c3:04.0/0000:c4:00.0 ar=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB : Made virtual device [2] name=ibp196s0 speed=100000 ndevs=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB: [3] ibp131s0:uverbs3:1/IB provider=Mlx5 speed=100000 context=0x5786f32430d0 pciPath=/sys/devices/pci0000:80/0000:80:01.1/0000:81:00.0/0000:82:04.0/0000:83:00.0 ar=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB : Made virtual device [3] name=ibp131s0 speed=100000 ndevs=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB: [4] ibp136s0:uverbs4:1/IB provider=Mlx5 speed=100000 context=0x5786f3284780 pciPath=/sys/devices/pci0000:80/0000:80:03.1/0000:86:00.0/0000:87:04.0/0000:88:00.0 ar=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB : Made virtual device [4] name=ibp136s0 speed=100000 ndevs=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB: [5] ibp3s0:uverbs5:1/IB provider=Mlx5 speed=100000 context=0x5786f32c5e30 pciPath=/sys/devices/pci0000:00/0000:00:01.1/0000:01:00.0/0000:02:04.0/0000:03:00.0 ar=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB : Made virtual device [5] name=ibp3s0 speed=100000 ndevs=1
ubuntu:1038246:1038246 [1] NCCL INFO NET/IB : Using [0]rocep193s0f0:1/RoCE [1]rocep193s0f1:1/RoCE [2]ibp196s0:1/IB [3]ibp131s0:1/IB [4]ibp136s0:1/IB [5]ibp3s0:1/IB [RO]; OOB lo:127.0.0.1<0>
ubuntu:1038246:1038246 [1] NCCL INFO Initialized NET plugin IB
ubuntu:1038246:1038246 [1] NCCL INFO NET/Socket : Using [0]lo:127.0.0.1<0>
ubuntu:1038246:1038246 [1] NCCL INFO Initialized NET plugin Socket
ubuntu:1038246:1038246 [1] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038246:1038246 [1] NCCL INFO Assigned NET plugin Socket to comm
ubuntu:1038246:1038246 [1] NCCL INFO Using network Socket
ubuntu:1038245:1038245 [0] NCCL INFO ncclCommInitRank comm 0x5c3d5e8b26b0 rank 0 nranks 2 cudaDev 0 nvmlDev 0 busId 4000 commId 0x56b314fa62eb59a1 - Init START
ubuntu:1038246:1038246 [1] NCCL INFO ncclCommInitRank comm 0x5786ef4b9fd0 rank 1 nranks 2 cudaDev 1 nvmlDev 1 busId 5000 commId 0x56b314fa62eb59a1 - Init START
ubuntu:1038246:1038246 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
ubuntu:1038245:1038245 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
ubuntu:1038245:1038245 [0] NCCL INFO NCCL Proxy 启用独立进程模式
ubuntu:1038246:1038246 [1] NCCL INFO NCCL Proxy 启用独立进程模式
ubuntu:1038245:1038245 [0] NCCL INFO 启动proxy service进程成功，PID: 1038281
ubuntu:1038246:1038246 [1] NCCL INFO 启动proxy service进程成功，PID: 1038282
ubuntu:1038281:1038281 [0] NCCL INFO ====== NCCL Proxy Service 独立进程启动 ======
ubuntu:1038281:1038281 [0] NCCL INFO 进程ID: 1038281, 线程ID: 125476872437760
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 解析启动参数: rank = 0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] Rank 0 将监听端口: 50051 (地址: 127.0.0.1:50051)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 注册信号处理器 (SIGINT, SIGTERM)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 开始初始化Proxy Service状态...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 初始化Proxy Service状态
ubuntu:1038281:1038281 [0] NCCL INFO NCCL_SOCKET_IFNAME set to lo
ubuntu:1038281:1038281 [0] NCCL INFO NET/Socket : Using [0]lo:127.0.0.1<0>
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 加载网络插件: Socket, 设备数: 1
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Proxy Service状态初始化完成
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] Proxy Service状态初始化完成
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 解析监听地址: 127.0.0.1:50051
ubuntu:1038281:1038281 [0] NCCL INFO 开始解析地址: 127.0.0.1:50051
ubuntu:1038281:1038281 [0] NCCL INFO 地址解析成功: 127.0.0.1:50051 -> 127.0.0.1<50051>
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 地址解析成功
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 创建监听Socket...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] ✓ Proxy Service成功启动，正在监听地址: 127.0.0.1:50051
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] ✓ 服务已准备就绪，等待客户端连接...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 进入主事件循环，支持连接复用...
ubuntu:1038282:1038282 [0] NCCL INFO ====== NCCL Proxy Service 独立进程启动 ======
ubuntu:1038282:1038282 [0] NCCL INFO 进程ID: 1038282, 线程ID: 137240548847616
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 解析启动参数: rank = 1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] Rank 1 将监听端口: 50052 (地址: 127.0.0.1:50052)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 注册信号处理器 (SIGINT, SIGTERM)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 开始初始化Proxy Service状态...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 初始化Proxy Service状态
ubuntu:1038282:1038282 [0] NCCL INFO NCCL_SOCKET_IFNAME set to lo
ubuntu:1038282:1038282 [0] NCCL INFO NET/Socket : Using [0]lo:127.0.0.1<0>
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 加载网络插件: Socket, 设备数: 1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Proxy Service状态初始化完成
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] Proxy Service状态初始化完成
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 解析监听地址: 127.0.0.1:50052
ubuntu:1038282:1038282 [0] NCCL INFO 开始解析地址: 127.0.0.1:50052
ubuntu:1038282:1038282 [0] NCCL INFO 地址解析成功: 127.0.0.1:50052 -> 127.0.0.1<50052>
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 地址解析成功
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 创建监听Socket...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] ✓ Proxy Service成功启动，正在监听地址: 127.0.0.1:50052
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] ✓ 服务已准备就绪，等待客户端连接...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 进入主事件循环，支持连接复用...
ubuntu:1038245:1038245 [0] NCCL INFO NCCL_P2P_DISABLE set by environment to 1
ubuntu:1038246:1038246 [1] NCCL INFO NCCL_P2P_DISABLE set by environment to 1
ubuntu:1038246:1038246 [1] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038246:1038246 [1] NCCL INFO NET/Socket : GPU Direct RDMA Disabled for HCA 0 'lo'
ubuntu:1038245:1038245 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038245:1038245 [0] NCCL INFO NET/Socket : GPU Direct RDMA Disabled for HCA 0 'lo'
ubuntu:1038246:1038246 [1] NCCL INFO comm 0x5786ef4b9fd0 rank 1 nRanks 2 nNodes 2 localRanks 1 localRank 0 MNNVL 0
ubuntu:1038246:1038246 [1] NCCL INFO Trees [0] -1/-1/-1->1->0 [1] 0/-1/-1->1->-1
ubuntu:1038246:1038246 [1] NCCL INFO P2P Chunksize set to 131072
ubuntu:1038245:1038245 [0] NCCL INFO comm 0x5c3d5e8b26b0 rank 0 nRanks 2 nNodes 2 localRanks 1 localRank 0 MNNVL 0
ubuntu:1038245:1038245 [0] NCCL INFO Channel 00/02 : 0 1
ubuntu:1038245:1038245 [0] NCCL INFO Channel 01/02 : 0 1
ubuntu:1038245:1038245 [0] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] -1/-1/-1->0->1
ubuntu:1038245:1038245 [0] NCCL INFO P2P Chunksize set to 131072
ubuntu:1038246:1038246 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
ubuntu:1038246:1038246 [1] NCCL INFO 独立进程模式：跳过传统proxy线程创建
ubuntu:1038245:1038245 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
ubuntu:1038245:1038245 [0] NCCL INFO Check P2P Type isAllDirectP2p 0 directMode 0
ubuntu:1038245:1038245 [0] NCCL INFO 独立进程模式：跳过传统proxy线程创建
ubuntu:1038245:1038245 [0] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038245:1038245 [0] NCCL INFO RPC参数: type=1, reqSize=20, respSize=16, rank=0
ubuntu:1038245:1038245 [0] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038245:1038245 [0] NCCL INFO 初始化proxy service连接池，大小: 1
ubuntu:1038245:1038245 [0] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50051
ubuntu:1038245:1038245 [0] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038245:1038245 [0] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038245:1038245 [0] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038245:1038245 [0] NCCL INFO 发送消息类型: 1 (Init)
ubuntu:1038245:1038245 [0] NCCL INFO 发送reqSize=20, respSize=16
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#1)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #1 (fd=39)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #1 的请求
ubuntu:1038245:1038245 [0] NCCL INFO 发送请求数据，大小: 20 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 1 (Init)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=20, respSize=16
ubuntu:1038245:1038245 [0] NCCL INFO 发送opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 20 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: (nil)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理INIT请求
ubuntu:1038245:1038245 [0] NCCL INFO 等待接收RPC响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== 开始处理INIT RPC请求 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Init请求参数: transport=2, send=0, tpLocalRank=0, tpRank=0, sameProcess=1
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 创建连接对象成功: transport=2, send=0, tcomm=0x582037f9c5c0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1000, 指针=0x5820598fde80, 当前句柄总数=1
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 连接需要proxy progress，创建共享内存池
ubuntu:1038246:1038246 [1] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038246:1038246 [1] NCCL INFO RPC参数: type=1, reqSize=20, respSize=16, rank=1
ubuntu:1038246:1038246 [1] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038246:1038246 [1] NCCL INFO 初始化proxy service连接池，大小: 1
ubuntu:1038246:1038246 [1] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50052
ubuntu:1038246:1038246 [1] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038246:1038246 [1] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038246:1038246 [1] NCCL INFO 发送消息类型: 1 (Init)
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#1)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #1 (fd=39)
ubuntu:1038246:1038246 [1] NCCL INFO 发送reqSize=20, respSize=16
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #1 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 1 (Init)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=20, respSize=16
ubuntu:1038246:1038246 [1] NCCL INFO 发送请求数据，大小: 20 字节
ubuntu:1038246:1038246 [1] NCCL INFO 发送opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 20 字节
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038246:1038246 [1] NCCL INFO 等待接收RPC响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理INIT请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== 开始处理INIT RPC请求 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Init请求参数: transport=2, send=0, tpLocalRank=0, tpRank=1, sameProcess=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 创建连接对象成功: transport=2, send=0, tcomm=0x64fd024535c0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1000, 指针=0x64fd32fb3e80, 当前句柄总数=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 连接需要proxy progress，创建共享内存池
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 成功创建共享内存: /dev/shm/nccl-77tygl, 大小: 33030528
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== INIT RPC处理完成，连接句柄: 1000 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=0, respSize=16
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #1 请求处理完成
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 接收到RPC响应头，respSize=16, res=0
ubuntu:1038245:1038245 [0] NCCL INFO 接收RPC响应载荷，大小: 16 字节
ubuntu:1038245:1038245 [0] NCCL INFO ✓ Init响应转换完成，connectionHandle=0x3e8 (1000)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #1 请求处理成功，保持连接活跃
ubuntu:1038245:1038245 [0] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038245:1038245 [0] NCCL INFO Connected to proxy localRank 0 -> connection 0x3e8
ubuntu:1038245:1038245 [0] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038245:1038245 [0] NCCL INFO RPC参数: type=3, reqSize=36, respSize=128, rank=0
ubuntu:1038245:1038245 [0] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038245:1038245 [0] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50051
ubuntu:1038245:1038245 [0] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#2)
ubuntu:1038245:1038245 [0] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #2 (fd=41)
ubuntu:1038245:1038245 [0] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038245:1038245 [0] NCCL INFO 发送消息类型: 3 (Setup)
ubuntu:1038245:1038245 [0] NCCL INFO 发送connection句柄: 0x3e8 (1000)
ubuntu:1038245:1038245 [0] NCCL INFO 发送reqSize=36, respSize=128
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #2 的请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 3 (Setup)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到connection指针: 0x3e8
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=36, respSize=128
ubuntu:1038245:1038245 [0] NCCL INFO 发送请求数据，大小: 36 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 36 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038245:1038245 [0] NCCL INFO 发送opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: (nil)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理SETUP请求
ubuntu:1038245:1038245 [0] NCCL INFO 等待接收RPC响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] Setup使用connectionHandle: 1000
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Setup请求参数: connectionHandle=1000, transportType=0, netDev=0, useGdr=0, needFlush=1041, shared=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Rank信息: tpRank=1, tpLocalRank=0, tpRemoteRank=0, channelId=0, connIndex=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，判断为: RECV Setup
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 执行RecvProxySetup逻辑...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 分配recvNetResources成功
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 获取网络设备 0 的属性...
ubuntu:1038281:1038281 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 网络设备属性: maxRecvs=1, ptrSupport=0x1, maxP2pBytes=1099511627776
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 资源配置: useDmaBuf=0, maxRecvs=1, maxP2pBytes=1099511627776
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 创建网络监听句柄...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 网络监听句柄创建成功，netComm=0x5820598c4f70
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1001, 指针=0x5820598bea20, 当前句柄总数=2
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 资源句柄注册成功: ID=1001, 指针=0x5820598bea20
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 映射connection 1000 -> resources 1001
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] RecvProxySetup完成，resourcesHandle=1001
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== Setup RPC成功完成，resourcesHandle=1001 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=0, respSize=128
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 接收到RPC响应头，respSize=128, res=0
ubuntu:1038245:1038245 [0] NCCL INFO 接收RPC响应载荷，大小: 128 字节
ubuntu:1038245:1038245 [0] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038245:1038245 [0] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #2 请求处理完成
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #2 请求处理成功，保持连接活跃
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038245:1038245 [0] NCCL INFO Channel 00/0 : 1[1] -> 0[0] [receive] via NET/Socket/0
ubuntu:1038245:1038245 [0] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038245:1038245 [0] NCCL INFO RPC参数: type=1, reqSize=20, respSize=16, rank=0
ubuntu:1038245:1038245 [0] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038245:1038245 [0] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50051
ubuntu:1038245:1038245 [0] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038245:1038245 [0] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038245:1038245 [0] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038245:1038245 [0] NCCL INFO 发送消息类型: 1 (Init)
ubuntu:1038245:1038245 [0] NCCL INFO 发送reqSize=20, respSize=16
ubuntu:1038245:1038245 [0] NCCL INFO 发送请求数据，大小: 20 字节
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#3)
ubuntu:1038245:1038245 [0] NCCL INFO 发送opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #3 (fd=43)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #3 的请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 1 (Init)
ubuntu:1038245:1038245 [0] NCCL INFO 等待接收RPC响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=20, respSize=16
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 20 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理INIT请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== 开始处理INIT RPC请求 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Init请求参数: transport=2, send=0, tpLocalRank=0, tpRank=0, sameProcess=1
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 创建连接对象成功: transport=2, send=0, tcomm=0x582037f9c5c0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1002, 指针=0x5820598c5080, 当前句柄总数=3
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 连接需要proxy progress，创建共享内存池
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 成功创建共享内存: /dev/shm/nccl-breUAD, 大小: 33030528
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== INIT RPC处理完成，连接句柄: 1000 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=0, respSize=16
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 接收到RPC响应头，respSize=16, res=0
ubuntu:1038246:1038246 [1] NCCL INFO 接收RPC响应载荷，大小: 16 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #1 请求处理完成
ubuntu:1038246:1038246 [1] NCCL INFO ✓ Init响应转换完成，connectionHandle=0x3e8 (1000)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038246:1038246 [1] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #1 请求处理成功，保持连接活跃
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038246:1038246 [1] NCCL INFO Connected to proxy localRank 0 -> connection 0x3e8
ubuntu:1038246:1038246 [1] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038246:1038246 [1] NCCL INFO RPC参数: type=3, reqSize=36, respSize=128, rank=1
ubuntu:1038246:1038246 [1] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038246:1038246 [1] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50052
ubuntu:1038246:1038246 [1] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038246:1038246 [1] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#2)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #2 (fd=41)
ubuntu:1038246:1038246 [1] NCCL INFO 发送消息类型: 3 (Setup)
ubuntu:1038246:1038246 [1] NCCL INFO 发送connection句柄: 0x3e8 (1000)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #2 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 3 (Setup)
ubuntu:1038246:1038246 [1] NCCL INFO 发送reqSize=36, respSize=128
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到connection指针: 0x3e8
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=36, respSize=128
ubuntu:1038246:1038246 [1] NCCL INFO 发送请求数据，大小: 36 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 36 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038246:1038246 [1] NCCL INFO 发送opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: (nil)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理SETUP请求
ubuntu:1038246:1038246 [1] NCCL INFO 等待接收RPC响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] Setup使用connectionHandle: 1000
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Setup请求参数: connectionHandle=1000, transportType=0, netDev=0, useGdr=0, needFlush=1041, shared=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Rank信息: tpRank=0, tpLocalRank=0, tpRemoteRank=0, channelId=0, connIndex=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，判断为: RECV Setup
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 执行RecvProxySetup逻辑...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 分配recvNetResources成功
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 获取网络设备 0 的属性...
ubuntu:1038282:1038282 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 网络设备属性: maxRecvs=1, ptrSupport=0x1, maxP2pBytes=1099511627776
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 资源配置: useDmaBuf=0, maxRecvs=1, maxP2pBytes=1099511627776
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 创建网络监听句柄...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 网络监听句柄创建成功，netComm=0x64fd32f7af60
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1001, 指针=0x64fd32f74a10, 当前句柄总数=2
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 资源句柄注册成功: ID=1001, 指针=0x64fd32f74a10
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 映射connection 1000 -> resources 1001
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] RecvProxySetup完成，resourcesHandle=1001
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== Setup RPC成功完成，resourcesHandle=1001 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=0, respSize=128
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 接收到RPC响应头，respSize=128, res=0
ubuntu:1038246:1038246 [1] NCCL INFO 接收RPC响应载荷，大小: 128 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #2 请求处理完成
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #2 请求处理成功，保持连接活跃
ubuntu:1038246:1038246 [1] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038246:1038246 [1] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038246:1038246 [1] NCCL INFO Channel 00/0 : 0[0] -> 1[1] [receive] via NET/Socket/0
ubuntu:1038246:1038246 [1] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038246:1038246 [1] NCCL INFO RPC参数: type=1, reqSize=20, respSize=16, rank=1
ubuntu:1038246:1038246 [1] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038246:1038246 [1] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50052
ubuntu:1038246:1038246 [1] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#3)
ubuntu:1038246:1038246 [1] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #3 (fd=43)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038246:1038246 [1] NCCL INFO 发送消息类型: 1 (Init)
ubuntu:1038246:1038246 [1] NCCL INFO 发送reqSize=20, respSize=16
ubuntu:1038246:1038246 [1] NCCL INFO 发送请求数据，大小: 20 字节
ubuntu:1038246:1038246 [1] NCCL INFO 发送opId: (nil)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038246:1038246 [1] NCCL INFO 等待接收RPC响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #3 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 1 (Init)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=20, respSize=16
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 20 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理INIT请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== 开始处理INIT RPC请求 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Init请求参数: transport=2, send=0, tpLocalRank=0, tpRank=1, sameProcess=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 创建连接对象成功: transport=2, send=0, tcomm=0x64fd024535c0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1002, 指针=0x64fd32f7b070, 当前句柄总数=3
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 连接需要proxy progress，创建共享内存池
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 成功创建共享内存: /dev/shm/nccl-r1BuuC, 大小: 33030528
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== INIT RPC处理完成，连接句柄: 1002 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=0, respSize=16
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #3 请求处理完成
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 接收到RPC响应头，respSize=16, res=0
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #3 请求处理成功，保持连接活跃
ubuntu:1038245:1038245 [0] NCCL INFO 接收RPC响应载荷，大小: 16 字节
ubuntu:1038245:1038245 [0] NCCL INFO ✓ Init响应转换完成，connectionHandle=0x3ea (1002)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038245:1038245 [0] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038245:1038245 [0] NCCL INFO Connected to proxy localRank 0 -> connection 0x3ea
ubuntu:1038245:1038245 [0] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038245:1038245 [0] NCCL INFO RPC参数: type=3, reqSize=36, respSize=128, rank=0
ubuntu:1038245:1038245 [0] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038245:1038245 [0] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50051
ubuntu:1038245:1038245 [0] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#4)
ubuntu:1038245:1038245 [0] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #4 (fd=45)
ubuntu:1038245:1038245 [0] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038245:1038245 [0] NCCL INFO 发送消息类型: 3 (Setup)
ubuntu:1038245:1038245 [0] NCCL INFO 发送connection句柄: 0x3ea (1002)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #4 的请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 3 (Setup)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到connection指针: 0x3ea
ubuntu:1038245:1038245 [0] NCCL INFO 发送reqSize=36, respSize=128
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=36, respSize=128
ubuntu:1038245:1038245 [0] NCCL INFO 发送请求数据，大小: 36 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 36 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038245:1038245 [0] NCCL INFO 发送opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: (nil)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理SETUP请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] Setup使用connectionHandle: 1002
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Setup请求参数: connectionHandle=1002, transportType=0, netDev=0, useGdr=0, needFlush=657, shared=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Rank信息: tpRank=1, tpLocalRank=0, tpRemoteRank=0, channelId=0, connIndex=1
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，判断为: RECV Setup
ubuntu:1038245:1038245 [0] NCCL INFO 等待接收RPC响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 执行RecvProxySetup逻辑...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 分配recvNetResources成功
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 获取网络设备 0 的属性...
ubuntu:1038281:1038281 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 网络设备属性: maxRecvs=1, ptrSupport=0x1, maxP2pBytes=1099511627776
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 资源配置: useDmaBuf=0, maxRecvs=1, maxP2pBytes=1099511627776
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 创建网络监听句柄...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 网络监听句柄创建成功，netComm=0x5820598c5200
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1003, 指针=0x5820598bf080, 当前句柄总数=4
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 资源句柄注册成功: ID=1003, 指针=0x5820598bf080
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 映射connection 1002 -> resources 1003
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] RecvProxySetup完成，resourcesHandle=1003
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== Setup RPC成功完成，resourcesHandle=1003 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=0, respSize=128
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 接收到RPC响应头，respSize=128, res=0
ubuntu:1038245:1038245 [0] NCCL INFO 接收RPC响应载荷，大小: 128 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #4 请求处理完成
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #4 请求处理成功，保持连接活跃
ubuntu:1038245:1038245 [0] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038245:1038245 [0] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038245:1038245 [0] NCCL INFO Channel 01/0 : 1[1] -> 0[0] [receive] via NET/Socket/0
ubuntu:1038245:1038245 [0] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038245:1038245 [0] NCCL INFO RPC参数: type=1, reqSize=20, respSize=16, rank=0
ubuntu:1038245:1038245 [0] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038245:1038245 [0] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50051
ubuntu:1038245:1038245 [0] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038245:1038245 [0] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#5)
ubuntu:1038245:1038245 [0] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #5 (fd=47)
ubuntu:1038245:1038245 [0] NCCL INFO 发送消息类型: 1 (Init)
ubuntu:1038245:1038245 [0] NCCL INFO 发送reqSize=20, respSize=16
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #5 的请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 1 (Init)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=20, respSize=16
ubuntu:1038245:1038245 [0] NCCL INFO 发送请求数据，大小: 20 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 20 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038245:1038245 [0] NCCL INFO 发送opId: (nil)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038245:1038245 [0] NCCL INFO 等待接收RPC响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理INIT请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== 开始处理INIT RPC请求 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Init请求参数: transport=2, send=1, tpLocalRank=0, tpRank=0, sameProcess=1
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 创建连接对象成功: transport=2, send=1, tcomm=0x582037f9c570
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1004, 指针=0x5820598c5310, 当前句柄总数=5
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 连接需要proxy progress，创建共享内存池
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 成功创建共享内存: /dev/shm/nccl-9RhZWg, 大小: 33030528
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== INIT RPC处理完成，连接句柄: 1002 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=0, respSize=16
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #3 请求处理完成
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #3 请求处理成功，保持连接活跃
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 接收到RPC响应头，respSize=16, res=0
ubuntu:1038246:1038246 [1] NCCL INFO 接收RPC响应载荷，大小: 16 字节
ubuntu:1038246:1038246 [1] NCCL INFO ✓ Init响应转换完成，connectionHandle=0x3ea (1002)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038246:1038246 [1] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038246:1038246 [1] NCCL INFO Connected to proxy localRank 0 -> connection 0x3ea
ubuntu:1038246:1038246 [1] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038246:1038246 [1] NCCL INFO RPC参数: type=3, reqSize=36, respSize=128, rank=1
ubuntu:1038246:1038246 [1] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038246:1038246 [1] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50052
ubuntu:1038246:1038246 [1] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038246:1038246 [1] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#4)
ubuntu:1038246:1038246 [1] NCCL INFO 发送消息类型: 3 (Setup)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #4 (fd=45)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #4 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 3 (Setup)
ubuntu:1038246:1038246 [1] NCCL INFO 发送connection句柄: 0x3ea (1002)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到connection指针: 0x3ea
ubuntu:1038246:1038246 [1] NCCL INFO 发送reqSize=36, respSize=128
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=36, respSize=128
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 36 字节
ubuntu:1038246:1038246 [1] NCCL INFO 发送请求数据，大小: 36 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038246:1038246 [1] NCCL INFO 发送opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: (nil)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理SETUP请求
ubuntu:1038246:1038246 [1] NCCL INFO 等待接收RPC响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] Setup使用connectionHandle: 1002
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Setup请求参数: connectionHandle=1002, transportType=0, netDev=0, useGdr=0, needFlush=673, shared=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Rank信息: tpRank=0, tpLocalRank=0, tpRemoteRank=0, channelId=0, connIndex=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，判断为: RECV Setup
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 执行RecvProxySetup逻辑...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 分配recvNetResources成功
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 获取网络设备 0 的属性...
ubuntu:1038282:1038282 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 网络设备属性: maxRecvs=1, ptrSupport=0x1, maxP2pBytes=1099511627776
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 资源配置: useDmaBuf=0, maxRecvs=1, maxP2pBytes=1099511627776
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 创建网络监听句柄...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 网络监听句柄创建成功，netComm=0x64fd32f7b1f0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1003, 指针=0x64fd32f75070, 当前句柄总数=4
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 资源句柄注册成功: ID=1003, 指针=0x64fd32f75070
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 映射connection 1002 -> resources 1003
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] RecvProxySetup完成，resourcesHandle=1003
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== Setup RPC成功完成，resourcesHandle=1003 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=0, respSize=128
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 接收到RPC响应头，respSize=128, res=0
ubuntu:1038246:1038246 [1] NCCL INFO 接收RPC响应载荷，大小: 128 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #4 请求处理完成
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #4 请求处理成功，保持连接活跃
ubuntu:1038246:1038246 [1] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038246:1038246 [1] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038246:1038246 [1] NCCL INFO Channel 01/0 : 0[0] -> 1[1] [receive] via NET/Socket/0
ubuntu:1038246:1038246 [1] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038246:1038246 [1] NCCL INFO RPC参数: type=1, reqSize=20, respSize=16, rank=1
ubuntu:1038246:1038246 [1] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038246:1038246 [1] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50052
ubuntu:1038246:1038246 [1] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#5)
ubuntu:1038246:1038246 [1] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #5 (fd=47)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038246:1038246 [1] NCCL INFO 发送消息类型: 1 (Init)
ubuntu:1038246:1038246 [1] NCCL INFO 发送reqSize=20, respSize=16
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #5 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 1 (Init)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=20, respSize=16
ubuntu:1038246:1038246 [1] NCCL INFO 发送请求数据，大小: 20 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 20 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038246:1038246 [1] NCCL INFO 发送opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: (nil)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理INIT请求
ubuntu:1038246:1038246 [1] NCCL INFO 等待接收RPC响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== 开始处理INIT RPC请求 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Init请求参数: transport=2, send=1, tpLocalRank=0, tpRank=1, sameProcess=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 创建连接对象成功: transport=2, send=1, tcomm=0x64fd02453570
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1004, 指针=0x64fd32f7b300, 当前句柄总数=5
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 连接需要proxy progress，创建共享内存池
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 成功创建共享内存: /dev/shm/nccl-4cbu97, 大小: 33030528
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== INIT RPC处理完成，连接句柄: 1004 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=0, respSize=16
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 接收到RPC响应头，respSize=16, res=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #5 请求处理完成
ubuntu:1038245:1038245 [0] NCCL INFO 接收RPC响应载荷，大小: 16 字节
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #5 请求处理成功，保持连接活跃
ubuntu:1038245:1038245 [0] NCCL INFO ✓ Init响应转换完成，connectionHandle=0x3ec (1004)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038245:1038245 [0] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038245:1038245 [0] NCCL INFO Connected to proxy localRank 0 -> connection 0x3ec
ubuntu:1038245:1038245 [0] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038245:1038245 [0] NCCL INFO RPC参数: type=3, reqSize=36, respSize=0, rank=0
ubuntu:1038245:1038245 [0] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038245:1038245 [0] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50051
ubuntu:1038245:1038245 [0] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#6)
ubuntu:1038245:1038245 [0] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #6 (fd=49)
ubuntu:1038245:1038245 [0] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038245:1038245 [0] NCCL INFO 发送消息类型: 3 (Setup)
ubuntu:1038245:1038245 [0] NCCL INFO 发送connection句柄: 0x3ec (1004)
ubuntu:1038245:1038245 [0] NCCL INFO 发送reqSize=36, respSize=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #6 的请求
ubuntu:1038245:1038245 [0] NCCL INFO 发送请求数据，大小: 36 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 3 (Setup)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到connection指针: 0x3ec
ubuntu:1038245:1038245 [0] NCCL INFO 发送opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=36, respSize=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 36 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: (nil)
ubuntu:1038245:1038245 [0] NCCL INFO 等待接收RPC响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理SETUP请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] Setup使用connectionHandle: 1004
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Setup请求参数: connectionHandle=1004, transportType=0, netDev=0, useGdr=0, needFlush=257, shared=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Rank信息: tpRank=1, tpLocalRank=0, tpRemoteRank=0, channelId=0, connIndex=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，判断为: RECV Setup
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 执行RecvProxySetup逻辑...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 分配recvNetResources成功
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 获取网络设备 0 的属性...
ubuntu:1038281:1038281 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 网络设备属性: maxRecvs=1, ptrSupport=0x1, maxP2pBytes=1099511627776
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 资源配置: useDmaBuf=0, maxRecvs=1, maxP2pBytes=1099511627776
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 创建网络监听句柄...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 网络监听句柄创建成功，netComm=0x5820598c0150
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1005, 指针=0x5820598bfaf0, 当前句柄总数=6
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 资源句柄注册成功: ID=1005, 指针=0x5820598bfaf0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 映射connection 1004 -> resources 1005
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] RecvProxySetup完成，resourcesHandle=1005
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== Setup RPC成功完成，resourcesHandle=1005 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=0, respSize=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #6 请求处理完成
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #6 请求处理成功，保持连接活跃
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 接收到RPC响应头，respSize=0, res=0
ubuntu:1038245:1038245 [0] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038245:1038245 [0] NCCL INFO Channel 00/0 : 0[0] -> 1[1] [send] via NET/Socket/0
ubuntu:1038245:1038245 [0] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038245:1038245 [0] NCCL INFO RPC参数: type=1, reqSize=20, respSize=16, rank=0
ubuntu:1038245:1038245 [0] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038245:1038245 [0] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50051
ubuntu:1038245:1038245 [0] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038245:1038245 [0] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#7)
ubuntu:1038245:1038245 [0] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #7 (fd=51)
ubuntu:1038245:1038245 [0] NCCL INFO 发送消息类型: 1 (Init)
ubuntu:1038245:1038245 [0] NCCL INFO 发送reqSize=20, respSize=16
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #7 的请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 1 (Init)
ubuntu:1038245:1038245 [0] NCCL INFO 发送请求数据，大小: 20 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=20, respSize=16
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 20 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038245:1038245 [0] NCCL INFO 发送opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: (nil)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理INIT请求
ubuntu:1038245:1038245 [0] NCCL INFO 等待接收RPC响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== 开始处理INIT RPC请求 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Init请求参数: transport=2, send=1, tpLocalRank=0, tpRank=0, sameProcess=1
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 创建连接对象成功: transport=2, send=1, tcomm=0x582037f9c570
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1006, 指针=0x5820598c0260, 当前句柄总数=7
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 连接需要proxy progress，创建共享内存池
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 成功创建共享内存: /dev/shm/nccl-D48THc, 大小: 33030528
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== INIT RPC处理完成，连接句柄: 1004 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=0, respSize=16
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 接收到RPC响应头，respSize=16, res=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #5 请求处理完成
ubuntu:1038246:1038246 [1] NCCL INFO 接收RPC响应载荷，大小: 16 字节
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #5 请求处理成功，保持连接活跃
ubuntu:1038246:1038246 [1] NCCL INFO ✓ Init响应转换完成，connectionHandle=0x3ec (1004)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038246:1038246 [1] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038246:1038246 [1] NCCL INFO Connected to proxy localRank 0 -> connection 0x3ec
ubuntu:1038246:1038246 [1] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038246:1038246 [1] NCCL INFO RPC参数: type=3, reqSize=36, respSize=0, rank=1
ubuntu:1038246:1038246 [1] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038246:1038246 [1] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50052
ubuntu:1038246:1038246 [1] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#6)
ubuntu:1038246:1038246 [1] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #6 (fd=49)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038246:1038246 [1] NCCL INFO 发送消息类型: 3 (Setup)
ubuntu:1038246:1038246 [1] NCCL INFO 发送connection句柄: 0x3ec (1004)
ubuntu:1038246:1038246 [1] NCCL INFO 发送reqSize=36, respSize=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #6 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 3 (Setup)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到connection指针: 0x3ec
ubuntu:1038246:1038246 [1] NCCL INFO 发送请求数据，大小: 36 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=36, respSize=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 36 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038246:1038246 [1] NCCL INFO 发送opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: (nil)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038246:1038246 [1] NCCL INFO 等待接收RPC响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理SETUP请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] Setup使用connectionHandle: 1004
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Setup请求参数: connectionHandle=1004, transportType=0, netDev=0, useGdr=0, needFlush=0, shared=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Rank信息: tpRank=0, tpLocalRank=0, tpRemoteRank=0, channelId=0, connIndex=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，判断为: SEND Setup
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 执行SendProxySetup逻辑...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 分配sendNetResources成功
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Setup参数赋值后: shared=0 (请求中的值=0)
ubuntu:1038282:1038282 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 资源配置: shared=0, useDmaBuf=0, maxRecvs=1, maxP2pBytes=1099511627776
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1005, 指针=0x64fd32f75ae0, 当前句柄总数=6
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 映射connection 1004 -> resources 1005
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== Setup RPC成功完成，resourcesHandle=1005 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=0, respSize=0
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 接收到RPC响应头，respSize=0, res=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #6 请求处理完成
ubuntu:1038246:1038246 [1] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #6 请求处理成功，保持连接活跃
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038246:1038246 [1] NCCL INFO Channel 00/0 : 1[1] -> 0[0] [send] via NET/Socket/0
ubuntu:1038246:1038246 [1] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038246:1038246 [1] NCCL INFO RPC参数: type=1, reqSize=20, respSize=16, rank=1
ubuntu:1038246:1038246 [1] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038246:1038246 [1] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50052
ubuntu:1038246:1038246 [1] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#7)
ubuntu:1038246:1038246 [1] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #7 (fd=50)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038246:1038246 [1] NCCL INFO 发送消息类型: 1 (Init)
ubuntu:1038246:1038246 [1] NCCL INFO 发送reqSize=20, respSize=16
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #7 的请求
ubuntu:1038246:1038246 [1] NCCL INFO 发送请求数据，大小: 20 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 1 (Init)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=20, respSize=16
ubuntu:1038246:1038246 [1] NCCL INFO 发送opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 20 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: (nil)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理INIT请求
ubuntu:1038246:1038246 [1] NCCL INFO 等待接收RPC响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== 开始处理INIT RPC请求 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Init请求参数: transport=2, send=1, tpLocalRank=0, tpRank=1, sameProcess=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 创建连接对象成功: transport=2, send=1, tcomm=0x64fd02453570
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1006, 指针=0x64fd32f761e0, 当前句柄总数=7
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 连接需要proxy progress，创建共享内存池
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 成功创建共享内存: /dev/shm/nccl-hGl4W5, 大小: 33030528
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== INIT RPC处理完成，连接句柄: 1006 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=0, respSize=16
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #7 请求处理完成
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 接收到RPC响应头，respSize=16, res=0
ubuntu:1038245:1038245 [0] NCCL INFO 接收RPC响应载荷，大小: 16 字节
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #7 请求处理成功，保持连接活跃
ubuntu:1038245:1038245 [0] NCCL INFO ✓ Init响应转换完成，connectionHandle=0x3ee (1006)
ubuntu:1038245:1038245 [0] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038245:1038245 [0] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038245:1038245 [0] NCCL INFO Connected to proxy localRank 0 -> connection 0x3ee
ubuntu:1038245:1038245 [0] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038245:1038245 [0] NCCL INFO RPC参数: type=3, reqSize=36, respSize=0, rank=0
ubuntu:1038245:1038245 [0] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038245:1038245 [0] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50051
ubuntu:1038245:1038245 [0] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#8)
ubuntu:1038245:1038245 [0] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #8 (fd=53)
ubuntu:1038245:1038245 [0] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038245:1038245 [0] NCCL INFO 发送消息类型: 3 (Setup)
ubuntu:1038245:1038245 [0] NCCL INFO 发送connection句柄: 0x3ee (1006)
ubuntu:1038245:1038245 [0] NCCL INFO 发送reqSize=36, respSize=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #8 的请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 3 (Setup)
ubuntu:1038245:1038245 [0] NCCL INFO 发送请求数据，大小: 36 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到connection指针: 0x3ee
ubuntu:1038245:1038245 [0] NCCL INFO 发送opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=36, respSize=0
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038245:1038245 [0] NCCL INFO 等待接收RPC响应...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 36 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: (nil)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理SETUP请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] Setup使用connectionHandle: 1006
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Setup请求参数: connectionHandle=1006, transportType=0, netDev=0, useGdr=1179692, needFlush=18737, shared=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Rank信息: tpRank=1, tpLocalRank=0, tpRemoteRank=0, channelId=0, connIndex=1
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，判断为: RECV Setup
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 执行RecvProxySetup逻辑...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 分配recvNetResources成功
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 获取网络设备 0 的属性...
ubuntu:1038281:1038281 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 网络设备属性: maxRecvs=1, ptrSupport=0x1, maxP2pBytes=1099511627776
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 资源配置: useDmaBuf=0, maxRecvs=1, maxP2pBytes=1099511627776
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 创建网络监听句柄...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 网络监听句柄创建成功，netComm=0x5820598c0ab0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1007, 指针=0x5820598c0450, 当前句柄总数=8
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ✓ 资源句柄注册成功: ID=1007, 指针=0x5820598c0450
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 映射connection 1006 -> resources 1007
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] RecvProxySetup完成，resourcesHandle=1007
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== Setup RPC成功完成，resourcesHandle=1007 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=0, respSize=0
ubuntu:1038245:1038245 [0] NCCL INFO ✓ 接收到RPC响应头，respSize=0, res=0
ubuntu:1038245:1038245 [0] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #8 请求处理完成
ubuntu:1038245:1038245 [0] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #8 请求处理成功，保持连接活跃
ubuntu:1038245:1038245 [0] NCCL INFO Channel 01/0 : 0[0] -> 1[1] [send] via NET/Socket/0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 成功创建共享内存: /dev/shm/nccl-xsLQMo, 大小: 33030528
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== INIT RPC处理完成，连接句柄: 1006 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=0, respSize=16
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 接收到RPC响应头，respSize=16, res=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #7 请求处理完成
ubuntu:1038246:1038246 [1] NCCL INFO 接收RPC响应载荷，大小: 16 字节
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #7 请求处理成功，保持连接活跃
ubuntu:1038246:1038246 [1] NCCL INFO ✓ Init响应转换完成，connectionHandle=0x3ee (1006)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ RPC响应载荷接收完成
ubuntu:1038246:1038246 [1] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038246:1038246 [1] NCCL INFO Connected to proxy localRank 0 -> connection 0x3ee
ubuntu:1038246:1038246 [1] NCCL INFO ====== 开始RPC远程调用 ======
ubuntu:1038246:1038246 [1] NCCL INFO RPC参数: type=3, reqSize=36, respSize=0, rank=1
ubuntu:1038246:1038246 [1] NCCL INFO 从连接池获取到proxy service的Socket连接...
ubuntu:1038246:1038246 [1] NCCL INFO 为tpLocalRank 0建立新连接到proxy service: 127.0.0.1:50052
ubuntu:1038246:1038246 [1] NCCL INFO ✓ tpLocalRank 0连接建立成功
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#8)
ubuntu:1038246:1038246 [1] NCCL INFO 获取连接: tpLocalRank=0, refCount=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #8 (fd=52)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 成功获取复用Socket连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 准备发送原始NCCL协议消息...
ubuntu:1038246:1038246 [1] NCCL INFO 发送消息类型: 3 (Setup)
ubuntu:1038246:1038246 [1] NCCL INFO 发送connection句柄: 0x3ee (1006)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #8 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 3 (Setup)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到connection指针: 0x3ee
ubuntu:1038246:1038246 [1] NCCL INFO 发送reqSize=36, respSize=0
ubuntu:1038246:1038246 [1] NCCL INFO 发送请求数据，大小: 36 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=36, respSize=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 36 字节
ubuntu:1038246:1038246 [1] NCCL INFO 发送opId: (nil)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: (nil)
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 请求发送完成，等待响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理SETUP请求
ubuntu:1038246:1038246 [1] NCCL INFO 等待接收RPC响应...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] Setup使用connectionHandle: 1006
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Setup请求参数: connectionHandle=1006, transportType=0, netDev=0, useGdr=458852, needFlush=18849, shared=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Rank信息: tpRank=0, tpLocalRank=0, tpRemoteRank=0, channelId=0, connIndex=1
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，判断为: RECV Setup
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 执行RecvProxySetup逻辑...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 分配recvNetResources成功
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 获取网络设备 0 的属性...
ubuntu:1038282:1038282 [0] NCCL INFO Could not get speed from /sys/class/net/lo/speed. Defaulting to 10 Gbps.
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 网络设备属性: maxRecvs=1, ptrSupport=0x1, maxP2pBytes=1099511627776
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 资源配置: useDmaBuf=0, maxRecvs=1, maxP2pBytes=1099511627776
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 创建网络监听句柄...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 网络监听句柄创建成功，netComm=0x64fd32f76a30
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 注册新句柄: ID=1007, 指针=0x64fd32f763d0, 当前句柄总数=8
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ✓ 资源句柄注册成功: ID=1007, 指针=0x64fd32f763d0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 映射connection 1006 -> resources 1007
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] RecvProxySetup完成，resourcesHandle=1007
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== Setup RPC成功完成，resourcesHandle=1007 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=0, respSize=0
ubuntu:1038246:1038246 [1] NCCL INFO ✓ 接收到RPC响应头，respSize=0, res=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #8 请求处理完成
ubuntu:1038246:1038246 [1] NCCL INFO ====== RPC远程调用成功完成 ======
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #8 请求处理成功，保持连接活跃
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接池连接 (tpLocalRank=0)
ubuntu:1038246:1038246 [1] NCCL INFO 释放连接: tpLocalRank=0, refCount=0
ubuntu:1038246:1038246 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] [send] via NET/Socket/0
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#9)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #9 (fd=54)
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#9)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #9 (fd=55)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #9 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到消息类型: 4 (Connect)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到connection指针: 0x3ec
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #9 的请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 消息大小: reqSize=132, respSize=1400
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收请求数据，大小: 132 字节
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 请求数据接收完成
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 接收到opId: 0x5786f33b0620
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理CONNECT请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] ====== 开始处理CONNECT RPC请求 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] Connect请求参数: resourcesHandle=72058143084445698, transportType=0, isRecv=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 当前句柄表中有 8 个条目
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service]   句柄 1000 -> 0x64fd32fb3e80
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service]   句柄 1001 -> 0x64fd32f74a10
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service]   句柄 1002 -> 0x64fd32f7b070
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service]   句柄 1003 -> 0x64fd32f75070
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service]   句柄 1004 -> 0x64fd32f7b300
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service]   句柄 1005 -> 0x64fd32f75ae0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service]   句柄 1006 -> 0x64fd32f761e0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service]   句柄 1007 -> 0x64fd32f763d0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，处理方向: SEND
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Service] 处理SendProxyConnect

[2025-08-02 09:31:57] ubuntu:1038282:1038282 [0] proxy_service.cc:68 NCCL WARN [Proxy-Service] 句柄查找失败: ID=72058143084445698

[2025-08-02 09:31:57] ubuntu:1038282:1038282 [0] proxy_service.cc:627 NCCL WARN [Proxy-Service] SEND: 无效的resourcesHandle: 72058143084445698

[2025-08-02 09:31:57] ubuntu:1038282:1038282 [0] proxy_service.cc:801 NCCL WARN [Proxy-Service] ====== Connect RPC处理失败，错误码: 4 ======
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 发送响应: res=4, respSize=0
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 连接 #9 请求处理完成
ubuntu:1038282:1038282 [0] NCCL INFO 连接 #9 请求处理成功，保持连接活跃
ubuntu:1038282:1038282 [0] NCCL INFO ✓ 新客户端连接成功 (#10)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 添加新连接 #10 (fd=55)
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 处理连接 #9 的请求
ubuntu:1038282:1038282 [0] NCCL INFO [Proxy-Rank-1] 等待接收消息类型...
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到消息类型: 4 (Connect)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到connection指针: 0x3ec
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 消息大小: reqSize=132, respSize=1400
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收请求数据，大小: 132 字节
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 请求数据接收完成
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 接收到opId: 0x5c3d627a88e8
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理CONNECT请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] ====== 开始处理CONNECT RPC请求 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] Connect请求参数: resourcesHandle=72058139526299650, transportType=0, isRecv=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 当前句柄表中有 8 个条目
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service]   句柄 1000 -> 0x5820598fde80
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service]   句柄 1001 -> 0x5820598bea20
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service]   句柄 1002 -> 0x5820598c5080
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service]   句柄 1003 -> 0x5820598bf080
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service]   句柄 1004 -> 0x5820598c5310
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service]   句柄 1005 -> 0x5820598bfaf0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service]   句柄 1006 -> 0x5820598c0260
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service]   句柄 1007 -> 0x5820598c0450
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 检测到NET传输类型，处理方向: SEND
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Service] 处理SendProxyConnect

[2025-08-02 09:31:57] ubuntu:1038281:1038281 [0] proxy_service.cc:68 NCCL WARN [Proxy-Service] 句柄查找失败: ID=72058139526299650

[2025-08-02 09:31:57] ubuntu:1038281:1038281 [0] proxy_service.cc:627 NCCL WARN [Proxy-Service] SEND: 无效的resourcesHandle: 72058139526299650

[2025-08-02 09:31:57] ubuntu:1038281:1038281 [0] proxy_service.cc:801 NCCL WARN [Proxy-Service] ====== Connect RPC处理失败，错误码: 4 ======
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 发送响应: res=4, respSize=0
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 连接 #9 请求处理完成
ubuntu:1038281:1038281 [0] NCCL INFO 连接 #9 请求处理成功，保持连接活跃
ubuntu:1038281:1038281 [0] NCCL INFO ✓ 新客户端连接成功 (#10)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 添加新连接 #10 (fd=56)
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 处理连接 #9 的请求
ubuntu:1038281:1038281 [0] NCCL INFO [Proxy-Rank-0] 等待接收消息类型...
^Cmake: *** [Makefile:47: test-local] Interrupt