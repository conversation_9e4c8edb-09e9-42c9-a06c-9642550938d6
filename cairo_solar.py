# -*- coding: utf-8 -*-
import cairo
import numpy as np
from PIL import Image
import math


def draw_stars(ctx, width, height, num_stars=200):
    """绘制星空背景"""
    for i in range(num_stars):
        # 使用确定性函数替代随机数
        x = int((math.sin(i * 0.3) * 0.5 + 0.5) * width)
        y = int((math.cos(i * 0.7) * 0.5 + 0.5) * height)
        size = 0.5 + 1.5 * (math.sin(i * 0.5) * 0.5 + 0.5)
        brightness = 0.3 + 0.7 * (math.cos(i * 1.1) * 0.5 + 0.5)
        
        # 绘制星星光晕
        star_gradient = cairo.RadialGradient(x, y, 0, x, y, size * 2)
        star_gradient.add_color_stop_rgba(0, 1, 1, 1, brightness)
        star_gradient.add_color_stop_rgba(0.5, 1, 1, 0.9, brightness * 0.5)
        star_gradient.add_color_stop_rgba(1, 1, 1, 0.8, 0)
        
        ctx.set_source(star_gradient)
        ctx.arc(x, y, size * 2, 0, 2 * math.pi)
        ctx.fill()
        
        # 绘制星星核心
        ctx.set_source_rgba(1, 1, 1, brightness)
        ctx.arc(x, y, size * 0.5, 0, 2 * math.pi)
        ctx.fill()


def draw_distant_galaxies(ctx, width, height, num_galaxies=3):
    """绘制遥远的星系"""
    for i in range(num_galaxies):
        # 使用确定性函数替代随机数
        x = int(100 + (width - 200) * (math.sin(i * 2.5) * 0.5 + 0.5))
        y = int(100 + (height - 200) * (math.cos(i * 1.8) * 0.5 + 0.5))
        size = 20 + 20 * (math.sin(i * 3.7) * 0.5 + 0.5)
        rotation = (math.cos(i * 1.3) * 0.5 + 0.5) * 2 * math.pi
        
        ctx.save()
        ctx.translate(x, y)
        ctx.rotate(rotation)
        
        # 绘制星系核心
        galaxy_gradient = cairo.RadialGradient(0, 0, 0, 0, 0, size)
        galaxy_gradient.add_color_stop_rgba(0, 0.9, 0.8, 1, 0.3)
        galaxy_gradient.add_color_stop_rgba(0.3, 0.7, 0.6, 0.9, 0.2)
        galaxy_gradient.add_color_stop_rgba(0.7, 0.5, 0.4, 0.8, 0.1)
        galaxy_gradient.add_color_stop_rgba(1, 0.3, 0.2, 0.7, 0)
        
        ctx.set_source(galaxy_gradient)
        ctx.scale(1, 0.3)  # 扁平化效果
        ctx.arc(0, 0, size, 0, 2 * math.pi)
        ctx.fill()
        
        ctx.restore()


def draw_nebula_layers(ctx, center_x, center_y):
    """绘制多层星云效果"""
    # 第一层 - 大型紫色星云
    nebula1 = cairo.RadialGradient(center_x - 100, center_y - 50, 0, 
                                   center_x - 100, center_y - 50, 300)
    nebula1.add_color_stop_rgba(0, 0.6, 0.2, 0.8, 0.15)
    nebula1.add_color_stop_rgba(0.5, 0.4, 0.1, 0.6, 0.08)
    nebula1.add_color_stop_rgba(1, 0.3, 0.05, 0.5, 0)
    
    ctx.set_source(nebula1)
    ctx.paint()
    
    # 第二层 - 蓝绿色星云
    nebula2 = cairo.RadialGradient(center_x + 150, center_y + 100, 0,
                                   center_x + 150, center_y + 100, 250)
    nebula2.add_color_stop_rgba(0, 0.1, 0.4, 0.7, 0.12)
    nebula2.add_color_stop_rgba(0.5, 0.05, 0.3, 0.6, 0.06)
    nebula2.add_color_stop_rgba(1, 0.02, 0.2, 0.5, 0)
    
    ctx.set_source(nebula2)
    ctx.paint()
    
    # 第三层 - 橙红色星云
    nebula3 = cairo.RadialGradient(center_x + 50, center_y - 150, 0,
                                   center_x + 50, center_y - 150, 200)
    nebula3.add_color_stop_rgba(0, 0.8, 0.3, 0.1, 0.1)
    nebula3.add_color_stop_rgba(0.5, 0.6, 0.2, 0.05, 0.05)
    nebula3.add_color_stop_rgba(1, 0.4, 0.1, 0.02, 0)
    
    ctx.set_source(nebula3)
    ctx.paint()


def create_solar_system_with_cairo():
    """使用Cairo创建太阳系"""
    width, height = 800, 600
    
    # 创建Cairo表面和上下文
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 深空背景
    ctx.set_source_rgb(0.02, 0.02, 0.08)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2
    
    # 绘制远处的星系
    draw_distant_galaxies(ctx, width, height)
    
    # 绘制多层星云
    draw_nebula_layers(ctx, center_x, center_y)
    
    # 绘制星空
    draw_stars(ctx, width, height, 300)
    
    # 添加宇宙尘埃效果
    dust_gradient = cairo.LinearGradient(0, 0, width, height)
    dust_gradient.add_color_stop_rgba(0, 0.5, 0.3, 0.7, 0.05)
    dust_gradient.add_color_stop_rgba(0.5, 0.7, 0.4, 0.9, 0.03)
    dust_gradient.add_color_stop_rgba(1, 0.4, 0.2, 0.6, 0.05)
    ctx.set_source(dust_gradient)
    ctx.paint()
    
    # 绘制背景星云 - 使用径向渐变
    gradient = cairo.RadialGradient(center_x, center_y, 0, center_x, center_y, 250)
    gradient.add_color_stop_rgba(0, 0.3, 0.1, 0.5, 0.3)    # 紫色中心
    gradient.add_color_stop_rgba(0.5, 0.1, 0.2, 0.4, 0.2)  # 蓝色中间
    gradient.add_color_stop_rgba(1, 0.02, 0.02, 0.08, 0)   # 透明边缘
    
    ctx.set_source(gradient)
    ctx.arc(center_x, center_y, 250, 0, 2 * math.pi)
    ctx.fill()
    
    # 太阳 - 发光效果
    sun_gradient = cairo.RadialGradient(center_x, center_y, 0, center_x, center_y, 25)
    sun_gradient.add_color_stop_rgba(0, 1, 1, 0.8, 1)     # 白黄色中心
    sun_gradient.add_color_stop_rgba(0.3, 1, 0.8, 0.2, 1) # 橙色
    sun_gradient.add_color_stop_rgba(0.7, 1, 0.4, 0, 0.8) # 红橙色
    sun_gradient.add_color_stop_rgba(1, 1, 0.2, 0, 0.3)   # 红色光晕
    
    ctx.set_source(sun_gradient)
    ctx.arc(center_x, center_y, 25, 0, 2 * math.pi)
    ctx.fill()
    
    # 行星数据 [距离, 大小, 颜色(R,G,B), 名称]
    planets = [
        [60, 3, (0.8, 0.5, 0.3), "Mercury"],
        [85, 4, (1, 0.8, 0.4), "Venus"],
        [110, 5, (0.2, 0.6, 1), "Earth"],
        [140, 4, (1, 0.3, 0.1), "Mars"],
        [200, 12, (0.8, 0.6, 0.4), "Jupiter"],
        [250, 10, (0.9, 0.8, 0.6), "Saturn"],
        [300, 7, (0.4, 0.8, 0.9), "Uranus"],
        [340, 6, (0.2, 0.4, 1), "Neptune"],
        [380, 20, (1, 0.4, 0.7), "m78"]
    ]
    
    # 绘制轨道和行星
    for i, (distance, size, color, name) in enumerate(planets):
        # 绘制轨道 - 虚线椭圆
        ctx.set_source_rgba(0.3, 0.3, 0.5, 0.6)
        ctx.set_line_width(1)
        ctx.set_dash([5, 3])
        
        # 椭圆轨道
        ctx.save()
        ctx.translate(center_x, center_y)
        ctx.scale(1, 0.8)  # 创造椭圆效果
        ctx.arc(0, 0, distance, 0, 2 * math.pi)
        ctx.restore()
        ctx.stroke()
        
        # 计算行星位置 - 不同行星有不同的角度偏移
        angle = (i * 0.3 + 0.5) * math.pi  # 每个行星不同的起始角度
        planet_x = center_x + distance * math.cos(angle)
        planet_y = center_y + distance * 0.8 * math.sin(angle)  # 椭圆效果
        
        # 绘制行星光晕
        planet_gradient = cairo.RadialGradient(planet_x, planet_y, 0, planet_x, planet_y, size * 2)
        planet_gradient.add_color_stop_rgba(0, color[0], color[1], color[2], 1)
        planet_gradient.add_color_stop_rgba(0.5, color[0], color[1], color[2], 0.8)
        planet_gradient.add_color_stop_rgba(1, color[0] * 0.5, color[1] * 0.5, color[2] * 0.5, 0.2)
        
        ctx.set_source(planet_gradient)
        ctx.arc(planet_x, planet_y, size * 2, 0, 2 * math.pi)
        ctx.fill()
        
        # 绘制行星主体
        ctx.set_source_rgb(color[0], color[1], color[2])
        ctx.arc(planet_x, planet_y, size, 0, 2 * math.pi)
        ctx.fill()
        
        # 特殊效果 - 土星环
        if name == "Saturn":
            ctx.set_source_rgba(0.8, 0.7, 0.5, 0.7)
            ctx.set_line_width(3)
            ctx.set_dash([])
            
            # 内环
            ctx.save()
            ctx.translate(planet_x, planet_y)
            ctx.scale(1, 0.3)
            ctx.arc(0, 0, size + 5, 0, 2 * math.pi)
            ctx.restore()
            ctx.stroke()
            
            # 外环
            ctx.save()
            ctx.translate(planet_x, planet_y)
            ctx.scale(1, 0.3)
            ctx.arc(0, 0, size + 8, 0, 2 * math.pi)
            ctx.restore()
            ctx.stroke()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]  # BGRA to RGBA
    pil_image = Image.fromarray(img_array, 'RGBA')
    
    return pil_image


# 创建并直接展示太阳系图像  
solar_system_image = create_solar_system_with_cairo()
solar_system_image.show()