# -*- coding: utf-8 -*-
import cairo
import numpy as np
from PIL import Image
import math
import random


def create_geometric_pattern():
    """几何图案 - 神圣几何与多边形组合"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 深色背景
    ctx.set_source_rgba(0.05, 0.05, 0.15, 1)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2
    
    # 绘制生命之花图案
    def draw_flower_of_life(cx, cy, radius, layers=3):
        """绘制生命之花"""
        for layer in range(layers):
            current_radius = radius * (0.3 + layer * 0.3)
            circles_count = 6 if layer > 0 else 1
            
            for i in range(circles_count if layer > 0 else 1):
                if layer == 0:
                    # 中心圆
                    angle = 0
                    circle_x, circle_y = cx, cy
                else:
                    angle = i * 2 * math.pi / circles_count
                    circle_x = cx + current_radius * 0.8 * math.cos(angle)
                    circle_y = cy + current_radius * 0.8 * math.sin(angle)
                
                # 彩虹色相
                hue = (layer * 60 + i * 45) % 360
                r = 0.5 + 0.5 * math.sin(math.radians(hue))
                g = 0.5 + 0.5 * math.sin(math.radians(hue + 120))
                b = 0.5 + 0.5 * math.sin(math.radians(hue + 240))
                
                ctx.set_source_rgba(r, g, b, 0.6)
                ctx.set_line_width(2)
                ctx.arc(circle_x, circle_y, current_radius, 0, 2 * math.pi)
                ctx.stroke()
    
    # 绘制正多边形组合
    def draw_polygon(cx, cy, radius, sides, rotation=0):
        """绘制正多边形"""
        ctx.move_to(
            cx + radius * math.cos(rotation),
            cy + radius * math.sin(rotation)
        )
        
        for i in range(1, sides + 1):
            angle = rotation + i * 2 * math.pi / sides
            x = cx + radius * math.cos(angle)
            y = cy + radius * math.sin(angle)
            ctx.line_to(x, y)
        
        ctx.close_path()
    
    # 中央生命之花
    draw_flower_of_life(center_x, center_y, 80)
    
    # 外围几何图形
    for ring in range(3):
        ring_radius = 150 + ring * 80
        shapes_count = 6 + ring * 2
        
        for i in range(shapes_count):
            angle = i * 2 * math.pi / shapes_count
            shape_x = center_x + ring_radius * math.cos(angle)
            shape_y = center_y + ring_radius * math.sin(angle)
            
            # 不同的几何形状
            sides = 3 + (i + ring) % 6  # 3到8边形
            size = 25 - ring * 5
            
            # 颜色变化
            hue = (ring * 45 + i * 30) % 360
            r = 0.6 + 0.4 * math.sin(math.radians(hue))
            g = 0.4 + 0.4 * math.sin(math.radians(hue + 120))
            b = 0.7 + 0.3 * math.sin(math.radians(hue + 240))
            
            ctx.set_source_rgba(r, g, b, 0.7)
            ctx.set_line_width(1.5)
            
            draw_polygon(shape_x, shape_y, size, sides, angle)
            ctx.stroke()
            
            # 填充部分形状
            if i % 3 == 0:
                ctx.set_source_rgba(r, g, b, 0.3)
                draw_polygon(shape_x, shape_y, size, sides, angle)
                ctx.fill()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


def create_nature_simulation():
    """自然模拟 - 树木、水流、山脉的自然景观"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 天空渐变背景
    sky_gradient = cairo.LinearGradient(0, 0, 0, height)
    sky_gradient.add_color_stop_rgba(0, 0.6, 0.8, 1, 1)      # 天空蓝
    sky_gradient.add_color_stop_rgba(0.3, 0.8, 0.9, 1, 1)    # 浅蓝
    sky_gradient.add_color_stop_rgba(0.7, 0.4, 0.7, 0.3, 1)  # 地面绿
    sky_gradient.add_color_stop_rgba(1, 0.2, 0.5, 0.1, 1)    # 深绿
    
    ctx.set_source(sky_gradient)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    # 绘制山脉轮廓
    def draw_mountains():
        """绘制山脉"""
        mountain_points = []
        for x in range(0, width + 50, 30):
            # 使用正弦函数创建山峰
            base_height = height * 0.4
            variation = 100 * math.sin(x * 0.01) + 50 * math.sin(x * 0.03)
            y = base_height + variation
            mountain_points.append((x, y))
        
        # 绘制山脉轮廓
        if mountain_points:
            ctx.move_to(mountain_points[0][0], mountain_points[0][1])
            for x, y in mountain_points[1:]:
                ctx.line_to(x, y)
            ctx.line_to(width, height)
            ctx.line_to(0, height)
            ctx.close_path()
            
            # 山脉渐变
            mountain_gradient = cairo.LinearGradient(0, height * 0.3, 0, height * 0.8)
            mountain_gradient.add_color_stop_rgba(0, 0.4, 0.3, 0.5, 0.8)
            mountain_gradient.add_color_stop_rgba(1, 0.2, 0.4, 0.2, 0.9)
            
            ctx.set_source(mountain_gradient)
            ctx.fill()
    
    # 绘制分形树
    def draw_tree(start_x, start_y, length, angle, depth, base_color_shift=0):
        """绘制自然风格的分形树"""
        if depth <= 0 or length < 3:
            return
        
        end_x = start_x + length * math.cos(angle)
        end_y = start_y + length * math.sin(angle)
        
        # 树干颜色 - 从棕色到绿色
        depth_factor = max(0, 1 - depth / 8)
        brown_r = 0.4 + base_color_shift * 0.1
        brown_g = 0.2 + depth_factor * 0.3 + base_color_shift * 0.1
        brown_b = 0.1
        
        # 添加绿色到树叶
        if depth <= 3:
            brown_r = brown_r * 0.3 + 0.2
            brown_g = brown_g * 0.5 + 0.6
            brown_b = brown_b * 0.3 + 0.3
        
        line_width = max(0.5, depth * 1.5)
        ctx.set_source_rgba(brown_r, brown_g, brown_b, 0.8)
        ctx.set_line_width(line_width)
        ctx.move_to(start_x, start_y)
        ctx.line_to(end_x, end_y)
        ctx.stroke()
        
        # 在树叶末端添加叶子效果
        if depth <= 2:
            leaf_size = max(2, depth * 3)
            leaf_gradient = cairo.RadialGradient(end_x, end_y, 0, end_x, end_y, leaf_size)
            leaf_gradient.add_color_stop_rgba(0, 0.3, 0.8, 0.2, 0.7)
            leaf_gradient.add_color_stop_rgba(1, 0.1, 0.4, 0.1, 0.3)
            
            ctx.set_source(leaf_gradient)
            ctx.arc(end_x, end_y, leaf_size, 0, 2 * math.pi)
            ctx.fill()
        
        # 递归分支，添加自然随机性
        new_length = length * (0.7 + random.uniform(-0.1, 0.1))
        angle_spread = math.pi / 6 + random.uniform(-0.2, 0.2)
        
        # 主要分支
        draw_tree(end_x, end_y, new_length, angle - angle_spread, depth - 1, base_color_shift)
        draw_tree(end_x, end_y, new_length, angle + angle_spread, depth - 1, base_color_shift)
        
        # 偶尔添加第三个分支
        if depth > 4 and random.random() < 0.3:
            draw_tree(end_x, end_y, new_length * 0.8, angle, depth - 1, base_color_shift)
    
    # 绘制河流
    def draw_river():
        """绘制蜿蜒的河流"""
        river_points = []
        for x in range(0, width + 20, 15):
            # 蜿蜒的河流路径
            y = height * 0.75 + 30 * math.sin(x * 0.02) + 15 * math.sin(x * 0.05)
            river_points.append((x, y))
        
        # 绘制河流带宽度
        ctx.set_line_cap(cairo.LINE_CAP_ROUND)
        ctx.set_line_join(cairo.LINE_JOIN_ROUND)
        
        for i, (x, y) in enumerate(river_points[:-1]):
            next_x, next_y = river_points[i + 1]
            
            # 河水颜色
            water_gradient = cairo.LinearGradient(x, y - 10, x, y + 10)
            water_gradient.add_color_stop_rgba(0, 0.2, 0.4, 0.8, 0.7)
            water_gradient.add_color_stop_rgba(0.5, 0.4, 0.6, 0.9, 0.8)
            water_gradient.add_color_stop_rgba(1, 0.1, 0.3, 0.6, 0.6)
            
            ctx.set_source(water_gradient)
            ctx.set_line_width(8 + 3 * math.sin(i * 0.3))  # 变化的宽度
            ctx.move_to(x, y)
            ctx.line_to(next_x, next_y)
            ctx.stroke()
    
    # 绘制场景元素
    draw_mountains()
    
    # 种植多棵树
    random.seed(42)  # 固定随机种子以获得一致的结果
    tree_positions = [
        (150, height * 0.8, 40, -math.pi/2, 7, 0),
        (300, height * 0.75, 45, -math.pi/2 + 0.2, 8, 0.2),
        (500, height * 0.82, 35, -math.pi/2 - 0.1, 6, 0.4),
        (650, height * 0.78, 50, -math.pi/2, 8, 0.1),
        (80, height * 0.85, 25, -math.pi/2 + 0.3, 5, 0.3),
    ]
    
    for x, y, length, angle, depth, color_shift in tree_positions:
        draw_tree(x, y, length, angle, depth, color_shift)
    
    draw_river()
    
    # 添加云朵
    for i in range(5):
        cloud_x = 100 + i * 150
        cloud_y = 80 + i * 20
        
        cloud_gradient = cairo.RadialGradient(cloud_x, cloud_y, 0, cloud_x, cloud_y, 40)
        cloud_gradient.add_color_stop_rgba(0, 1, 1, 1, 0.8)
        cloud_gradient.add_color_stop_rgba(1, 0.9, 0.9, 0.95, 0.3)
        
        ctx.set_source(cloud_gradient)
        ctx.arc(cloud_x, cloud_y, 30, 0, 2 * math.pi)
        ctx.fill()
        ctx.arc(cloud_x + 20, cloud_y, 25, 0, 2 * math.pi)
        ctx.fill()
        ctx.arc(cloud_x - 15, cloud_y + 5, 20, 0, 2 * math.pi)
        ctx.fill()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


def create_abstract_art():
    """抽象艺术 - 色彩流动与动态形状"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 多层次背景
    bg_gradient = cairo.RadialGradient(width//2, height//2, 0, width//2, height//2, width//2)
    bg_gradient.add_color_stop_rgba(0, 0.1, 0.05, 0.2, 1)
    bg_gradient.add_color_stop_rgba(0.5, 0.2, 0.1, 0.4, 1)
    bg_gradient.add_color_stop_rgba(1, 0.05, 0.02, 0.1, 1)
    
    ctx.set_source(bg_gradient)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    # 绘制流动的抽象形状
    def draw_flowing_shape(center_x, center_y, base_radius, time_offset=0):
        """绘制流动的有机形状"""
        points = []
        segments = 20
        
        for i in range(segments + 1):
            angle = i * 2 * math.pi / segments
            # 创建有机的、波动的半径
            radius_variation = base_radius * (
                1 + 0.3 * math.sin(angle * 3 + time_offset) +
                0.2 * math.sin(angle * 5 + time_offset * 1.5) +
                0.1 * math.sin(angle * 8 + time_offset * 2)
            )
            
            x = center_x + radius_variation * math.cos(angle)
            y = center_y + radius_variation * math.sin(angle)
            points.append((x, y))
        
        return points
    
    # 绘制多个层次的流动形状
    for layer in range(8):
        # 每层的参数
        layer_time = layer * 0.8
        layer_alpha = max(0.1, 0.7 - layer * 0.08)
        
        # 形状位置（螺旋排列）
        spiral_angle = layer * math.pi / 3
        spiral_radius = 50 + layer * 40
        shape_x = width//2 + spiral_radius * math.cos(spiral_angle)
        shape_y = height//2 + spiral_radius * math.sin(spiral_angle)
        
        # 形状大小
        shape_radius = 80 - layer * 8
        
        # 获取流动形状的点
        shape_points = draw_flowing_shape(shape_x, shape_y, shape_radius, layer_time)
        
        # 创建形状的渐变
        shape_gradient = cairo.RadialGradient(shape_x, shape_y, 0, shape_x, shape_y, shape_radius)
        
        # 动态颜色
        hue1 = (layer * 45 + layer_time * 30) % 360
        hue2 = (hue1 + 180) % 360
        
        r1 = 0.5 + 0.5 * math.sin(math.radians(hue1))
        g1 = 0.5 + 0.5 * math.sin(math.radians(hue1 + 120))
        b1 = 0.5 + 0.5 * math.sin(math.radians(hue1 + 240))
        
        r2 = 0.3 + 0.4 * math.sin(math.radians(hue2))
        g2 = 0.3 + 0.4 * math.sin(math.radians(hue2 + 120))
        b2 = 0.3 + 0.4 * math.sin(math.radians(hue2 + 240))
        
        shape_gradient.add_color_stop_rgba(0, r1, g1, b1, layer_alpha)
        shape_gradient.add_color_stop_rgba(0.7, r2, g2, b2, layer_alpha * 0.7)
        shape_gradient.add_color_stop_rgba(1, r2 * 0.5, g2 * 0.5, b2 * 0.5, 0)
        
        # 绘制形状
        if shape_points:
            ctx.move_to(shape_points[0][0], shape_points[0][1])
            for x, y in shape_points[1:]:
                ctx.line_to(x, y)
            ctx.close_path()
            
            ctx.set_source(shape_gradient)
            ctx.fill()
    
    # 添加抽象的线条网络
    def draw_abstract_connections():
        """绘制抽象的连接线"""
        connection_points = []
        
        # 生成连接点
        for i in range(15):
            angle = i * 2 * math.pi / 15
            radius = 200 + 100 * math.sin(i * 0.7)
            x = width//2 + radius * math.cos(angle)
            y = height//2 + radius * math.sin(angle)
            connection_points.append((x, y))
        
        # 绘制连接线
        for i, (x1, y1) in enumerate(connection_points):
            for j, (x2, y2) in enumerate(connection_points):
                if i < j and (j - i) % 3 == 1:  # 只连接特定的点
                    # 连接线渐变
                    line_gradient = cairo.LinearGradient(x1, y1, x2, y2)
                    
                    # 动态颜色
                    color_phase = (i + j) * 30
                    r = 0.4 + 0.4 * math.sin(math.radians(color_phase))
                    g = 0.3 + 0.3 * math.sin(math.radians(color_phase + 120))
                    b = 0.6 + 0.4 * math.sin(math.radians(color_phase + 240))
                    
                    line_gradient.add_color_stop_rgba(0, r, g, b, 0.6)
                    line_gradient.add_color_stop_rgba(0.5, r * 1.2, g * 1.2, b * 1.2, 0.8)
                    line_gradient.add_color_stop_rgba(1, r * 0.8, g * 0.8, b * 0.8, 0.4)
                    
                    ctx.set_source(line_gradient)
                    ctx.set_line_width(2)
                    ctx.move_to(x1, y1)
                    ctx.line_to(x2, y2)
                    ctx.stroke()
    
    draw_abstract_connections()
    
    # 添加粒子效果
    for i in range(50):
        particle_x = random.uniform(0, width)
        particle_y = random.uniform(0, height)
        particle_size = random.uniform(1, 4)
        
        # 粒子颜色
        particle_hue = random.uniform(0, 360)
        pr = 0.6 + 0.4 * math.sin(math.radians(particle_hue))
        pg = 0.4 + 0.4 * math.sin(math.radians(particle_hue + 120))
        pb = 0.8 + 0.2 * math.sin(math.radians(particle_hue + 240))
        
        particle_gradient = cairo.RadialGradient(particle_x, particle_y, 0, particle_x, particle_y, particle_size)
        particle_gradient.add_color_stop_rgba(0, pr, pg, pb, 0.9)
        particle_gradient.add_color_stop_rgba(1, pr * 0.3, pg * 0.3, pb * 0.3, 0)
        
        ctx.set_source(particle_gradient)
        ctx.arc(particle_x, particle_y, particle_size, 0, 2 * math.pi)
        ctx.fill()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


def create_mathematical_art():
    """数学艺术 - 分形、螺旋和数学函数的可视化"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 深空背景
    bg_gradient = cairo.RadialGradient(width//2, height//2, 0, width//2, height//2, width//2)
    bg_gradient.add_color_stop_rgba(0, 0.02, 0.02, 0.1, 1)
    bg_gradient.add_color_stop_rgba(0.7, 0.01, 0.01, 0.05, 1)
    bg_gradient.add_color_stop_rgba(1, 0, 0, 0, 1)
    
    ctx.set_source(bg_gradient)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2
    
    # 绘制斐波那契螺旋
    def draw_fibonacci_spiral():
        """绘制斐波那契螺旋"""
        # 斐波那契数列
        fib = [1, 1]
        for i in range(10):
            fib.append(fib[-1] + fib[-2])
        
        # 绘制黄金螺旋
        spiral_points = []
        angle = 0
        for i in range(200):
            # 黄金角度
            golden_angle = 137.5 * math.pi / 180
            radius = 3 * math.sqrt(i)
            
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            spiral_points.append((x, y))
            
            angle += golden_angle
        
        # 绘制螺旋路径
        if spiral_points:
            for i, (x, y) in enumerate(spiral_points[:-1]):
                next_x, next_y = spiral_points[i + 1]
                
                # 颜色随角度变化
                color_phase = i * 5
                r = 0.5 + 0.5 * math.sin(math.radians(color_phase))
                g = 0.3 + 0.4 * math.sin(math.radians(color_phase + 120))
                b = 0.7 + 0.3 * math.sin(math.radians(color_phase + 240))
                
                ctx.set_source_rgba(r, g, b, max(0.3, 1 - i * 0.004))
                ctx.set_line_width(max(0.5, 3 - i * 0.01))
                ctx.move_to(x, y)
                ctx.line_to(next_x, next_y)
                ctx.stroke()
    
    # 绘制曼德博集合近似
    def draw_mandelbrot_approximation():
        """绘制简化的曼德博集合风格图案"""
        iterations = 20
        zoom = 150
        
        for px in range(0, width, 4):
            for py in range(0, height, 4):
                # 转换到复平面
                x0 = (px - center_x) / zoom
                y0 = (py - center_y) / zoom
                
                # 简化的迭代
                x, y = 0, 0
                iteration = 0
                
                while x*x + y*y <= 4 and iteration < iterations:
                    xtemp = x*x - y*y + x0
                    y = 2*x*y + y0
                    x = xtemp
                    iteration += 1
                
                # 根据迭代次数着色
                if iteration < iterations:
                    color_factor = iteration / iterations
                    r = color_factor * 0.8
                    g = (1 - color_factor) * 0.6
                    b = math.sin(color_factor * math.pi) * 0.9
                    
                    ctx.set_source_rgba(r, g, b, 0.7)
                    ctx.rectangle(px, py, 4, 4)
                    ctx.fill()
    
    # 绘制数学函数的极坐标图
    def draw_polar_equations():
        """绘制极坐标方程图案"""
        equations = [
            lambda t: 100 * (1 + math.cos(6*t)),  # 六瓣玫瑰
            lambda t: 80 * math.cos(4*t),          # 四叶草
            lambda t: 60 + 40 * math.sin(8*t),     # 八边星形
        ]
        
        for eq_idx, equation in enumerate(equations):
            for i in range(360):
                t = i * math.pi / 180
                try:
                    r = equation(t)
                    if r > 0:  # 只绘制正半径
                        x = center_x + r * math.cos(t)
                        y = center_y + r * math.sin(t)
                        
                        # 每个方程不同颜色
                        hue = eq_idx * 120 + i * 2
                        color_r = 0.4 + 0.6 * math.sin(math.radians(hue))
                        color_g = 0.3 + 0.5 * math.sin(math.radians(hue + 120))
                        color_b = 0.6 + 0.4 * math.sin(math.radians(hue + 240))
                        
                        # 绘制点
                        point_gradient = cairo.RadialGradient(x, y, 0, x, y, 3)
                        point_gradient.add_color_stop_rgba(0, color_r, color_g, color_b, 0.8)
                        point_gradient.add_color_stop_rgba(1, color_r * 0.5, color_g * 0.5, color_b * 0.5, 0.2)
                        
                        ctx.set_source(point_gradient)
                        ctx.arc(x, y, 2, 0, 2 * math.pi)
                        ctx.fill()
                except:
                    continue
    
    # 绘制分形树（数学版本）
    def draw_math_fractal(x, y, length, angle, depth, branch_ratio=0.7):
        """绘制数学分形树"""
        if depth <= 0 or length < 2:
            return
        
        end_x = x + length * math.cos(angle)
        end_y = y + length * math.sin(angle)
        
        # 数学风格的着色
        color_intensity = max(0.2, depth / 8)
        math_r = 0.2 + color_intensity * 0.6
        math_g = 0.5 + color_intensity * 0.4
        math_b = 0.8 + color_intensity * 0.2
        
        ctx.set_source_rgba(math_r, math_g, math_b, color_intensity)
        ctx.set_line_width(max(0.5, depth * 0.8))
        ctx.move_to(x, y)
        ctx.line_to(end_x, end_y)
        ctx.stroke()
        
        # 黄金比例分支
        golden_ratio = (1 + math.sqrt(5)) / 2
        angle_delta = math.pi / golden_ratio
        
        new_length = length * branch_ratio
        draw_math_fractal(end_x, end_y, new_length, angle - angle_delta, depth - 1, branch_ratio)
        draw_math_fractal(end_x, end_y, new_length, angle + angle_delta, depth - 1, branch_ratio)
    
    # 执行绘制
    draw_fibonacci_spiral()
    draw_mandelbrot_approximation()
    draw_polar_equations()
    
    # 在四个角落绘制分形树
    fractal_positions = [
        (100, 700, -math.pi/4),   # 左下
        (700, 700, -3*math.pi/4), # 右下
        (100, 100, math.pi/4),    # 左上
        (700, 100, 3*math.pi/4),  # 右上
    ]
    
    for fx, fy, fangle in fractal_positions:
        draw_math_fractal(fx, fy, 60, fangle, 7)
    
    # 添加数学常数的可视化（π, e, φ）
    constants = [
        (math.pi, "π", (150, 150)),
        (math.e, "e", (650, 150)),
        ((1 + math.sqrt(5)) / 2, "φ", (400, 650)),
    ]
    
    for const_val, const_name, (const_x, const_y) in constants:
        # 用常数值创建圆形图案
        rings = int(const_val * 10) % 8 + 3
        for ring in range(rings):
            ring_radius = 10 + ring * 8
            ring_alpha = max(0.2, 1 - ring * 0.15)
            
            # 颜色基于常数值
            ring_hue = (const_val * 100) % 360
            ring_r = 0.5 + 0.5 * math.sin(math.radians(ring_hue + ring * 45))
            ring_g = 0.3 + 0.4 * math.sin(math.radians(ring_hue + ring * 45 + 120))
            ring_b = 0.7 + 0.3 * math.sin(math.radians(ring_hue + ring * 45 + 240))
            
            ctx.set_source_rgba(ring_r, ring_g, ring_b, ring_alpha)
            ctx.set_line_width(2)
            ctx.arc(const_x, const_y, ring_radius, 0, 2 * math.pi)
            ctx.stroke()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


def create_combined_artwork():
    """创建四种艺术风格的组合作品"""
    # 创建四个单独的图像
    geometric = create_geometric_pattern()
    nature = create_nature_simulation()
    abstract = create_abstract_art()
    mathematical = create_mathematical_art()
    
    # 创建2x2的组合图像
    combined_width = 1600
    combined_height = 1600
    combined_image = Image.new('RGBA', (combined_width, combined_height), (0, 0, 0, 255))
    
    # 将四个图像拼接到组合图像中
    combined_image.paste(geometric, (0, 0))           # 左上：几何图案
    combined_image.paste(nature, (800, 0))            # 右上：自然模拟
    combined_image.paste(abstract, (0, 800))          # 左下：抽象艺术
    combined_image.paste(mathematical, (800, 800))    # 右下：数学艺术
    
    return combined_image, geometric, nature, abstract, mathematical


if __name__ == "__main__":
    print("正在生成综合艺术作品...")
    print("包含：几何图案、自然模拟、抽象艺术、数学艺术")
    
    # 设置随机种子以获得可重复的结果
    random.seed(42)
    
    # 生成所有图像
    combined, geometric, nature, abstract, mathematical = create_combined_artwork()
    
    # 保存图像
    print("\n保存图像文件...")
    combined.save("comprehensive_art_combined.png")
    geometric.save("geometric_pattern.png")
    nature.save("nature_simulation.png") 
    abstract.save("abstract_art.png")
    mathematical.save("mathematical_art.png")
    
    print("✓ 综合作品: comprehensive_art_combined.png")
    print("✓ 几何图案: geometric_pattern.png")
    print("✓ 自然模拟: nature_simulation.png")
    print("✓ 抽象艺术: abstract_art.png")
    print("✓ 数学艺术: mathematical_art.png")
    
    # 显示组合图像
    print("\n正在显示综合艺术作品...")
    combined.show()
    
    print("\n🎨 综合艺术生成器运行完成！")
    print("四种艺术风格已成功融合并保存为独立文件。")
