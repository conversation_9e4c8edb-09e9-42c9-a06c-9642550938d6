# -*- coding: utf-8 -*-
import cairo
import numpy as np
from PIL import Image
import math


def create_wave_mandala_fusion():
    """创建波浪干涉与花卉曼陀罗融合图案"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 深色渐变背景
    bg_gradient = cairo.RadialGradient(width//2, height//2, 0, width//2, height//2, width//2)
    bg_gradient.add_color_stop_rgba(0, 0.1, 0.1, 0.2, 1)
    bg_gradient.add_color_stop_rgba(1, 0.05, 0.05, 0.1, 1)
    ctx.set_source(bg_gradient)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2
    
    # 步骤1: 先绘制波浪干涉作为背景纹理
    # 创建多个波源形成花瓣状分布
    wave_sources = []
    num_sources = 8
    source_radius = 150
    
    for i in range(num_sources):
        angle = i * 2 * math.pi / num_sources
        sx = center_x + source_radius * math.cos(angle)
        sy = center_y + source_radius * math.sin(angle)
        wave_sources.append((sx, sy))
    
    # 计算波浪干涉纹理
    for y in range(0, height, 3):  # 降低分辨率提高性能
        for x in range(0, width, 3):
            wave_sum = 0
            
            # 计算所有波源的叠加
            for sx, sy in wave_sources:
                dist = math.sqrt((x - sx)**2 + (y - sy)**2)
                if dist > 0:
                    wave = math.sin(dist * 0.08) * math.exp(-dist * 0.003)
                    wave_sum += wave
            
            # 波浪强度
            intensity = abs(wave_sum)
            
            if intensity > 0.2:  # 只绘制有明显强度的区域
                # 根据距离中心的位置调整颜色
                dist_to_center = math.sqrt((x - center_x)**2 + (y - center_y)**2)
                color_factor = 1 - min(1, dist_to_center / (width // 2))
                
                # 波浪颜色 - 蓝紫色调
                r = intensity * 0.2 + color_factor * 0.3
                g = intensity * 0.4 + color_factor * 0.2
                b = intensity * 0.8 + color_factor * 0.4
                alpha = min(0.6, intensity * 1.5)
                
                ctx.set_source_rgba(r, g, b, alpha)
                ctx.rectangle(x, y, 3, 3)
                ctx.fill()
    
    # 步骤2: 在波浪基础上绘制曼陀罗花瓣
    for layer in range(4):
        radius = 100 + layer * 50
        petals = 6 + layer * 2
        
        for i in range(petals):
            angle = i * 2 * math.pi / petals
            
            ctx.save()
            ctx.translate(center_x, center_y)
            ctx.rotate(angle)
            
            # 花瓣颜色 - 与波浪形成对比的暖色调
            hue = (layer * 45 + i * 20) % 360
            base_r = 0.6 + 0.3 * math.sin(math.radians(hue))
            base_g = 0.3 + 0.3 * math.sin(math.radians(hue + 60))
            base_b = 0.2 + 0.2 * math.sin(math.radians(hue + 120))
            
            # 创建花瓣渐变，融入波浪效果
            petal_gradient = cairo.RadialGradient(0, -radius//2, 0, 0, -radius//2, radius//2)
            petal_gradient.add_color_stop_rgba(0, base_r, base_g, base_b, 0.7)
            petal_gradient.add_color_stop_rgba(0.4, base_r * 0.8, base_g * 0.8, base_b * 0.8, 0.5)
            petal_gradient.add_color_stop_rgba(0.8, base_r * 0.4, base_g * 0.4, base_b * 0.4, 0.3)
            petal_gradient.add_color_stop_rgba(1, 0, 0, 0, 0)
            
            ctx.set_source(petal_gradient)
            
            # 花瓣形状 - 更有机的曲线
            ctx.move_to(0, -radius//3)
            ctx.curve_to(-radius//8, -radius//2, -radius//8, -radius*4//5, 0, -radius)
            ctx.curve_to(radius//8, -radius*4//5, radius//8, -radius//2, 0, -radius//3)
            ctx.close_path()
            ctx.fill()
            
            # 添加花瓣边缘的波浪纹理
            ctx.set_source_rgba(base_r * 1.2, base_g * 1.2, base_b * 1.2, 0.4)
            ctx.set_line_width(2)
            ctx.stroke_preserve()
            
            ctx.restore()
    
    # 步骤3: 在波源位置绘制小花朵装饰
    for sx, sy in wave_sources:
        # 小花朵
        small_gradient = cairo.RadialGradient(sx, sy, 0, sx, sy, 25)
        small_gradient.add_color_stop_rgba(0, 1, 0.8, 0.4, 0.8)
        small_gradient.add_color_stop_rgba(0.6, 0.8, 0.4, 0.2, 0.6)
        small_gradient.add_color_stop_rgba(1, 0.4, 0.2, 0.1, 0.2)
        
        ctx.set_source(small_gradient)
        
        num_petals = 8  
        for j in range(num_petals):
            angle = j * 2 * math.pi / num_petals 
            ctx.save()
            ctx.translate(sx, sy)
            ctx.rotate(angle)
            
            ctx.move_to(0, -8)
            ctx.curve_to(-4, -12, -4, -18, 0, -20)
            ctx.curve_to(4, -18, 4, -12, 0, -8)
            ctx.close_path()
            ctx.fill()
            
            ctx.restore()
    
    # 步骤4: 中央核心 - 波浪与花卉的完美融合
    center_size = 60
    
    # 外层 - 波浪纹理环
    for r in range(30, center_size, 3):
        wave_ring = 0
        for i in range(16):  # 16个方向采样
            sample_angle = i * math.pi / 8
            sample_x = center_x + r * math.cos(sample_angle)
            sample_y = center_y + r * math.sin(sample_angle)
            
            # 计算该点的波浪强度
            for sx, sy in wave_sources[:4]:  # 只使用前4个波源
                dist = math.sqrt((sample_x - sx)**2 + (sample_y - sy)**2)
                if dist > 0:
                    wave_ring += math.sin(dist * 0.1) * math.exp(-dist * 0.005)
        
        wave_intensity = abs(wave_ring) / 16
        
        # 环形渐变
        ring_gradient = cairo.RadialGradient(center_x, center_y, r-1, center_x, center_y, r+1)
        ring_gradient.add_color_stop_rgba(0, 0.8 + wave_intensity * 0.2, 
                                        0.6 + wave_intensity * 0.3, 
                                        0.2 + wave_intensity * 0.5, 
                                        0.6)
        ring_gradient.add_color_stop_rgba(1, 0.8 + wave_intensity * 0.2, 
                                        0.6 + wave_intensity * 0.3, 
                                        0.2 + wave_intensity * 0.5, 
                                        0.3)
        
        ctx.set_source(ring_gradient)
        ctx.arc(center_x, center_y, r, 0, 2 * math.pi)
        ctx.stroke()
    
    # 内层 - 花卉核心
    core_gradient = cairo.RadialGradient(center_x, center_y, 0, center_x, center_y, 30)
    core_gradient.add_color_stop_rgba(0, 1, 0.9, 0.7, 1)
    core_gradient.add_color_stop_rgba(0.5, 1, 0.7, 0.3, 0.9)
    core_gradient.add_color_stop_rgba(1, 0.8, 0.4, 0.1, 0.7)
    
    ctx.set_source(core_gradient)
    ctx.arc(center_x, center_y, 30, 0, 2 * math.pi)
    ctx.fill()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]  # BGRA to RGBA
    return Image.fromarray(img_array, 'RGBA')


if __name__ == "__main__":
    wave_mandala1 = create_wave_mandala_fusion()
    wave_mandala1.save("wave_mandala_fusion.png")
    wave_mandala1.show()
    