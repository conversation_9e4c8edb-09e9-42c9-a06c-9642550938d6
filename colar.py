# -*- coding: utf-8 -*-
import cairo
import numpy as np
from PIL import Image
import math

def create_alternative_gradient():
    """创建另一种渐变方式的5x5圆形矩阵"""
    width, height = 800, 800
    
    # 创建Cairo表面和上下文
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 设置深色背景
    ctx.set_source_rgb(0.1, 0.1, 0.2)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    # 计算圆形参数
    rows, cols = 7, 7
    circle_radius = 45  # 减小半径以适应更多圆形
    spacing = 100       # 减小间距
    
    # 计算起始位置
    start_x = (width - (cols - 1) * spacing) // 2
    start_y = (height - (rows - 1) * spacing) // 2
    
    # 绘制圆形矩阵
    for row in range(rows):
        for col in range(cols):
            center_x = start_x + col * spacing
            center_y = start_y + row * spacing
            
            # 基于距离中心的距离计算渐变
            center_matrix_x = start_x + (cols - 1) * spacing // 2
            center_matrix_y = start_y + (rows - 1) * spacing // 2
            
            distance = math.sqrt((center_x - center_matrix_x)**2 + (center_y - center_matrix_y)**2)
            max_distance = math.sqrt((spacing * 2)**2 + (spacing * 2)**2)
            
            # 渐变因子
            gradient_factor = distance / max_distance
            
            # 
            if row == 5 and col == 3: 
                blue_intensity = 0  
            else:
                blue_intensity = 0.3 + gradient_factor * 0.7
            
            # 创建多层渐变效果
            circle_gradient = cairo.RadialGradient(center_x - 15, center_y - 15, 5,
                                                 center_x, center_y, circle_radius)
            
            if row == 5 and col == 3: 
                circle_gradient.add_color_stop_rgba(0, 0.05, 0.05, 0.05, 1)  
                circle_gradient.add_color_stop_rgba(0.3, 0.02, 0.02, 0.02, 1) 
                circle_gradient.add_color_stop_rgba(0.8, 0, 0, 0, 1)          
                circle_gradient.add_color_stop_rgba(1, 0, 0, 0, 1)                
            else:
                circle_gradient.add_color_stop_rgba(0, 0.8, 0.9, 1, 1)               # 亮白中心
                circle_gradient.add_color_stop_rgba(0.3, 0.3, 0.5, blue_intensity, 1) # 浅蓝
                circle_gradient.add_color_stop_rgba(0.8, 0, 0.2, blue_intensity, 1)   # 中蓝
                circle_gradient.add_color_stop_rgba(1, 0, 0, blue_intensity * 0.8, 1) # 深蓝边缘
            
            # 绘制圆形
            ctx.set_source(circle_gradient)
            ctx.arc(center_x, center_y, circle_radius, 0, 2 * math.pi)
            ctx.fill()
            
            # 添加发光效果
            glow_gradient = cairo.RadialGradient(center_x, center_y, circle_radius,
                                               center_x, center_y, circle_radius + 20)
            glow_gradient.add_color_stop_rgba(0, 0, 0.3, blue_intensity, 0.3)
            glow_gradient.add_color_stop_rgba(1, 0, 0.1, blue_intensity * 0.5, 0)
            
            ctx.set_source(glow_gradient)
            ctx.arc(center_x, center_y, circle_radius + 20, 0, 2 * math.pi)
            ctx.fill()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]  # BGRA to RGBA
    pil_image = Image.fromarray(img_array, 'RGBA')
    
    return pil_image


if __name__ == "__main__":
    gradient_circles2 = create_alternative_gradient()
    gradient_circles2.save("blue_gradient_circles_2.png")
    gradient_circles2.show()
