"""
Python 3D图形库示例
演示 matplotlib, plotly 和 VTK 的基本用法
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def matplotlib_3d_example():
    """Matplotlib 3D示例"""
    print("=== Matplotlib 3D示例 ===")
    
    # 创建数据
    x = np.linspace(-5, 5, 50)
    y = np.linspace(-5, 5, 50)
    X, Y = np.meshgrid(x, y)
    Z = np.sin(np.sqrt(X**2 + Y**2))
    
    # 创建3D图形
    fig = plt.figure(figsize=(12, 4))
    
    # 3D曲面图
    ax1 = fig.add_subplot(131, projection='3d')
    ax1.plot_surface(X, Y, Z, cmap='viridis', alpha=0.8)
    ax1.set_title('3D Surface Plot')
    
    # 3D散点图
    ax2 = fig.add_subplot(132, projection='3d')
    x_scatter = np.random.randn(100)
    y_scatter = np.random.randn(100)
    z_scatter = np.random.randn(100)
    colors = np.random.randn(100)
    ax2.scatter(x_scatter, y_scatter, z_scatter, c=colors, cmap='plasma')
    ax2.set_title('3D Scatter Plot')
    
    # 3D线图
    ax3 = fig.add_subplot(133, projection='3d')
    t = np.linspace(0, 4*np.pi, 100)
    x_line = np.cos(t)
    y_line = np.sin(t)
    z_line = t
    ax3.plot(x_line, y_line, z_line, 'b-', linewidth=2)
    ax3.set_title('3D Line Plot')
    
    plt.tight_layout()
    plt.savefig('/Users/<USER>/pythonProject/matplotlib_3d_demo.png', dpi=150, bbox_inches='tight')
    plt.show()

def plotly_3d_example():
    """Plotly 3D示例 (需要安装: pip install plotly)"""
    try:
        import plotly.graph_objects as go
        import plotly.express as px
        from plotly.subplots import make_subplots
        
        print("=== Plotly 3D示例 ===")
        
        # 创建数据
        x = np.linspace(-5, 5, 30)
        y = np.linspace(-5, 5, 30)
        X, Y = np.meshgrid(x, y)
        Z = np.sin(np.sqrt(X**2 + Y**2))
        
        # 创建3D曲面图
        fig = go.Figure(data=[go.Surface(x=X, y=Y, z=Z, colorscale='viridis')])
        fig.update_layout(
            title='Interactive 3D Surface with Plotly',
            scene=dict(
                xaxis_title='X Axis',
                yaxis_title='Y Axis',
                zaxis_title='Z Axis'
            ),
            width=800,
            height=600
        )
        
        # 保存为HTML文件
        fig.write_html('/Users/<USER>/pythonProject/plotly_3d_demo.html')
        print("Plotly图表已保存为 plotly_3d_demo.html")
        
        # 创建3D散点图
        df_scatter = px.data.iris()
        fig_scatter = px.scatter_3d(df_scatter, x='sepal_length', y='sepal_width', z='petal_width',
                                  color='species', title='3D Scatter Plot with Plotly')
        fig_scatter.write_html('/Users/<USER>/pythonProject/plotly_scatter_3d.html')
        
    except ImportError:
        print("Plotly未安装，跳过Plotly示例")
        print("安装命令: pip install plotly")

def vtk_example():
    """VTK 3D示例 (需要安装: pip install vtk)"""
    try:
        import vtk
        
        print("=== VTK 3D示例 ===")
        
        # 创建一个球体
        sphere = vtk.vtkSphereSource()
        sphere.SetRadius(1.0)
        sphere.SetThetaResolution(30)
        sphere.SetPhiResolution(30)
        
        # 创建mapper
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(sphere.GetOutputPort())
        
        # 创建actor
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetColor(0.8, 0.2, 0.2)  # 红色
        
        # 创建渲染器
        renderer = vtk.vtkRenderer()
        renderer.AddActor(actor)
        renderer.SetBackground(0.1, 0.1, 0.2)  # 深蓝色背景
        
        # 创建渲染窗口
        render_window = vtk.vtkRenderWindow()
        render_window.AddRenderer(renderer)
        render_window.SetSize(800, 600)
        render_window.SetWindowName("VTK 3D Example")
        
        # 创建交互器
        interactor = vtk.vtkRenderWindowInteractor()
        interactor.SetRenderWindow(render_window)
        
        # 开始渲染
        render_window.Render()
        print("VTK窗口已打开，关闭窗口继续...")
        interactor.Start()
        
    except ImportError:
        print("VTK未安装，跳过VTK示例")
        print("安装命令: pip install vtk")

def create_3d_art_example():
    """创建艺术性的3D图形"""
    print("=== 3D艺术图形示例 ===")
    
    # 创建螺旋星系效果
    fig = plt.figure(figsize=(15, 5))
    
    # 螺旋星系
    ax1 = fig.add_subplot(131, projection='3d')
    t = np.linspace(0, 6*np.pi, 1000)
    r = np.linspace(0.1, 3, 1000)
    x = r * np.cos(t)
    y = r * np.sin(t)
    z = 0.3 * np.sin(5*t) * r
    
    colors = plt.cm.plasma(t / t.max())
    for i in range(0, len(x)-1, 10):
        ax1.plot(x[i:i+10], y[i:i+10], z[i:i+10], 
                color=colors[i], alpha=0.7, linewidth=2)
    ax1.set_title('Spiral Galaxy')
    ax1.set_facecolor('black')
    
    # 3D曼德拉
    ax2 = fig.add_subplot(132, projection='3d')
    theta = np.linspace(0, 2*np.pi, 100)
    phi = np.linspace(0, np.pi, 50)
    THETA, PHI = np.meshgrid(theta, phi)
    
    # 创建复杂的球面图案
    R = 1 + 0.3 * np.sin(5*THETA) * np.sin(7*PHI)
    X = R * np.sin(PHI) * np.cos(THETA)
    Y = R * np.sin(PHI) * np.sin(THETA)
    Z = R * np.cos(PHI)
    
    ax2.plot_surface(X, Y, Z, cmap='plasma', alpha=0.8)
    ax2.set_title('3D Mandala Sphere')
    
    # 分形树
    ax3 = fig.add_subplot(133, projection='3d')
    
    def fractal_tree_3d(ax, x, y, z, dx, dy, dz, depth, length):
        if depth == 0:
            return
        
        # 绘制当前分支
        new_x, new_y, new_z = x + dx * length, y + dy * length, z + dz * length
        ax.plot([x, new_x], [y, new_y], [z, new_z], 
               color=plt.cm.viridis(depth/6), linewidth=depth*0.5+1)
        
        if depth > 1:
            # 创建子分支
            angle1, angle2 = np.pi/6, -np.pi/6
            
            # 计算新的方向向量
            cos_a1, sin_a1 = np.cos(angle1), np.sin(angle1)
            cos_a2, sin_a2 = np.cos(angle2), np.sin(angle2)
            
            # 左分支
            new_dx1 = dx * cos_a1 - dy * sin_a1
            new_dy1 = dx * sin_a1 + dy * cos_a1
            new_dz1 = dz + 0.1
            
            # 右分支
            new_dx2 = dx * cos_a2 - dy * sin_a2
            new_dy2 = dx * sin_a2 + dy * cos_a2
            new_dz2 = dz + 0.1
            
            fractal_tree_3d(ax, new_x, new_y, new_z, 
                           new_dx1, new_dy1, new_dz1, depth-1, length*0.7)
            fractal_tree_3d(ax, new_x, new_y, new_z, 
                           new_dx2, new_dy2, new_dz2, depth-1, length*0.7)
    
    # 绘制分形树
    fractal_tree_3d(ax3, 0, 0, 0, 0, 0, 1, 6, 2)
    ax3.set_title('3D Fractal Tree')
    ax3.set_zlim(0, 10)
    
    plt.tight_layout()
    plt.savefig('/Users/<USER>/pythonProject/3d_art_demo.png', dpi=150, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("Python 3D图形库演示")
    print("=" * 50)
    
    # 基础示例
    matplotlib_3d_example()
    plotly_3d_example()
    vtk_example()
    
    # 艺术性示例
    create_3d_art_example()
    
    print("\n推荐总结:")
    print("1. 入门学习: Matplotlib - 简单易用")
    print("2. 交互式图表: Plotly - 现代化界面")
    print("3. 专业应用: VTK - 功能强大")
    print("4. 游戏开发: PyOpenGL/Panda3D - 高性能")

if __name__ == "__main__":
    main()
