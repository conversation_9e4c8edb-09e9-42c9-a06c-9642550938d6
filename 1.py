import matplotlib.pyplot as plt
import numpy as np

def create_cosmic_spirograph_correct():
    """
    Generates the cosmic spirograph with a black background and the correct 8-pointed star.
    """
    fig, ax = plt.subplots(figsize=(10, 10))
    # --- THIS IS THE CRUCIAL FIX FOR THE BACKGROUND ---
    fig.patch.set_facecolor('black') # Sets the entire figure background to black
    # ---------------------------------------------------
    ax.set_facecolor('black')         # Sets the axes area background to black

    # 1. Background Stars
    # ... (rest of the code is the same) ...
    num_stars = 400
    star_coords = np.random.rand(num_stars, 2) * 24 - 12
    star_sizes = np.random.rand(num_stars) * 3 + 0.5
    star_alphas = np.random.rand(num_stars) * 0.7 + 0.3
    ax.scatter(star_coords[:, 0], star_coords[:, 1], s=star_sizes, 
               color='white', alpha=star_alphas, zorder=1)

    # 2. Outer Gold Spirograph (Hypotrochoid) - CORRECT
    t = np.linspace(0, 20 * np.pi, 2000)
    R1 = 8  # Correct value for 8 points
    r1 = 5
    d1 = 8
    x1 = (R1 - r1) * np.cos(t) + d1 * np.cos((R1 - r1) / r1 * t)
    y1 = (R1 - r1) * np.sin(t) - d1 * np.sin((R1 - r1) / r1 * t)
    ax.plot(x1, y1, color='gold', lw=1.5, zorder=3)

    # 3. Inner Cyan Spirograph (Epitrochoid)
    R2 = 5
    r2 = 2
    d2 = 3
    x2 = (R2 + r2) * np.cos(t) + d2 * np.cos((R2 + r2) / r2 * t)
    y2 = (R2 + r2) * np.sin(t) - d2 * np.sin((R2 + r2) / r2 * t)
    ax.plot(x2, y2, color='cyan', lw=1.2, zorder=4)

    # 4. Orbiting Moons
    orbit_radius = 10
    num_moons = 6
    orbit_path = plt.Circle((0, 0), orbit_radius, color='gray', fill=False, 
                            linestyle='--', alpha=0.5, zorder=2)
    ax.add_artist(orbit_path)
    
    moon_angles = np.linspace(0, 2 * np.pi, num_moons, endpoint=False)
    moon_x = orbit_radius * np.cos(moon_angles)
    moon_y = orbit_radius * np.sin(moon_angles)
    ax.scatter(moon_x, moon_y, s=30, color='white', zorder=5, 
               edgecolors='lightblue', lw=1)

    # --- Plot styling ---
    ax.set_aspect('equal', adjustable='box')
    ax.set_xlim(-12, 12)
    ax.set_ylim(-12, 12)
    plt.axis('off')
    plt.tight_layout()
    plt.show()

if __name__ == '__main__':
    create_cosmic_spirograph_correct()