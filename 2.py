import numpy as np
import matplotlib.pyplot as plt

# --- 正确的参数 ---
# R: 固定圆的半径
# r: 滚动圆的半径
# d: 描绘点到滚动圆中心的距离
# 尖角数量 N = R / gcd(R, r)
# 为了得到8个角，我们选择 R=8, r=3。 gcd(8, 3)=1, 所以 N=8/1=8。
R = 8
r = 3
d = 5

# 设置绘图点
t = np.linspace(0, 20 * np.pi, 2000)

# 下旋轮线参数方程
x = (R - r) * np.cos(t) + d * np.cos((R - r) / r * t)
y = (R - r) * np.sin(t) - d * np.sin((R - r) / r * t)

# --- 绘图 ---
# 使用深色背景以突出金色图案
plt.style.use('dark_background')
fig, ax = plt.subplots(figsize=(8, 8))

# 绘制曲线
ax.plot(x, y, color='#FFD700', linewidth=2) # 使用金色

# 设置坐标轴
ax.set_aspect('equal', adjustable='box')
plt.axis('off') # 关闭坐标轴显示，使其更像一幅艺术作品

# 保存为基准图像
# plt.savefig('correct_hypotrochoid_8_points.png', dpi=300, bbox_inches='tight', pad_inches=0.1)
plt.show()