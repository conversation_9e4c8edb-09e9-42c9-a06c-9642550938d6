# -*- coding: utf-8 -*-
import cairo
import numpy as np
from PIL import Image
import math


def create_kaleidoscope_pattern():
    """创建万花筒图案"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 黑色背景
    ctx.set_source_rgb(0, 0, 0)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2
    
    # 创建8个对称扇形
    for i in range(8):
        ctx.save()
        ctx.translate(center_x, center_y)
        ctx.rotate(i * math.pi / 4)
        
        # 绘制彩色三角形
        for j in range(20):
            angle = j * math.pi / 20
            radius = 50 + j * 15
            
            # 彩虹色彩
            hue = (i * 45 + j * 18) % 360
            r = 0.5 + 0.5 * math.sin(math.radians(hue))
            g = 0.5 + 0.5 * math.sin(math.radians(hue + 120))
            b = 0.5 + 0.5 * math.sin(math.radians(hue + 240))
            
            gradient = cairo.RadialGradient(0, 0, 0, 0, 0, radius)
            gradient.add_color_stop_rgba(0, r, g, b, 0.8)
            gradient.add_color_stop_rgba(1, r * 0.3, g * 0.3, b * 0.3, 0)
            
            ctx.set_source(gradient)
            ctx.move_to(0, 0)
            ctx.arc(0, 0, radius, -math.pi/8, math.pi/8)
            ctx.close_path()
            ctx.fill()
        
        ctx.restore()
    
    # 转换为PIL图像
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


def create_spiral_galaxy():
    """创建螺旋星系图案"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 深色背景
    ctx.set_source_rgb(0.05, 0.05, 0.1)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2
    
    # 绘制多条螺旋臂
    for arm in range(4):
        arm_offset = arm * math.pi / 2
        
        for i in range(500):
            t = i * 0.02
            radius = 5 + t * 8
            angle = t * 2 + arm_offset
            
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            
            if 0 <= x < width and 0 <= y < height:
                # 星点大小随距离变化
                size = max(1, 4 - t * 0.1)
                
                # 颜色从蓝白到红色渐变
                color_factor = min(1, t * 0.05)
                r = 0.3 + color_factor * 0.7
                g = 0.5 + color_factor * 0.3
                b = 1 - color_factor * 0.3
                alpha = max(0.1, 1 - t * 0.02)
                
                # 绘制发光星点
                star_gradient = cairo.RadialGradient(x, y, 0, x, y, size * 3)
                star_gradient.add_color_stop_rgba(0, r, g, b, alpha)
                star_gradient.add_color_stop_rgba(0.5, r * 0.8, g * 0.8, b * 0.8, alpha * 0.5)
                star_gradient.add_color_stop_rgba(1, r * 0.3, g * 0.3, b * 0.3, 0)
                
                ctx.set_source(star_gradient)
                ctx.arc(x, y, size * 3, 0, 2 * math.pi)
                ctx.fill()
    
    # 添加中央黑洞
    black_hole = cairo.RadialGradient(center_x, center_y, 0, center_x, center_y, 50)
    black_hole.add_color_stop_rgba(0, 0, 0, 0, 1)
    black_hole.add_color_stop_rgba(0.7, 0.2, 0.1, 0, 0.8)
    black_hole.add_color_stop_rgba(1, 0.8, 0.4, 0, 0.3)
    
    ctx.set_source(black_hole)
    ctx.arc(center_x, center_y, 50, 0, 2 * math.pi)
    ctx.fill()
    
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


def create_flower_mandala():
    """创建花卉曼陀罗图案"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 渐变背景
    bg_gradient = cairo.RadialGradient(width//2, height//2, 0, width//2, height//2, width//2)
    bg_gradient.add_color_stop_rgba(0, 0.9, 0.9, 1, 1)
    bg_gradient.add_color_stop_rgba(1, 0.6, 0.7, 0.9, 1)
    ctx.set_source(bg_gradient)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    center_x, center_y = width // 2, height // 2
    
    # 绘制多层花瓣
    for layer in range(5):
        radius = 80 + layer * 60
        petals = 6 + layer * 2
        
        for i in range(petals):
            angle = i * 2 * math.pi / petals
            
            ctx.save()
            ctx.translate(center_x, center_y)
            ctx.rotate(angle)
            
            # 花瓣颜色
            hue = (layer * 60 + i * 15) % 360
            r = 0.5 + 0.3 * math.sin(math.radians(hue))
            g = 0.3 + 0.4 * math.sin(math.radians(hue + 120))
            b = 0.6 + 0.4 * math.sin(math.radians(hue + 240))
            
            # 绘制花瓣
            petal_gradient = cairo.RadialGradient(0, -radius//2, 0, 0, -radius//2, radius//2)
            petal_gradient.add_color_stop_rgba(0, r, g, b, 0.8)
            petal_gradient.add_color_stop_rgba(0.7, r * 0.7, g * 0.7, b * 0.7, 0.6)
            petal_gradient.add_color_stop_rgba(1, r * 0.3, g * 0.3, b * 0.3, 0.2)
            
            ctx.set_source(petal_gradient)
            
            # 花瓣形状
            ctx.move_to(0, -radius//4)
            ctx.curve_to(-radius//6, -radius//2, -radius//6, -radius*3//4, 0, -radius)
            ctx.curve_to(radius//6, -radius*3//4, radius//6, -radius//2, 0, -radius//4)
            ctx.close_path()
            ctx.fill()
            
            ctx.restore()
    
    # 中心装饰
    center_gradient = cairo.RadialGradient(center_x, center_y, 0, center_x, center_y, 40)
    center_gradient.add_color_stop_rgba(0, 1, 1, 0.8, 1)
    center_gradient.add_color_stop_rgba(0.5, 1, 0.8, 0.2, 1)
    center_gradient.add_color_stop_rgba(1, 0.8, 0.4, 0, 0.8)
    
    ctx.set_source(center_gradient)
    ctx.arc(center_x, center_y, 40, 0, 2 * math.pi)
    ctx.fill()
    
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


def create_wave_interference():
    """创建波浪干涉图案"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 黑色背景
    ctx.set_source_rgb(0, 0, 0)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    # 两个波源
    source1_x, source1_y = width // 3, height // 2
    source2_x, source2_y = width * 2 // 3, height // 2
    
    # 计算每个像素的波浪干涉
    for y in range(0, height, 4):  # 降低分辨率以提高性能
        for x in range(0, width, 4):
            # 到两个波源的距离
            dist1 = math.sqrt((x - source1_x)**2 + (y - source1_y)**2)
            dist2 = math.sqrt((x - source2_x)**2 + (y - source2_y)**2)
            
            # 波浪函数
            wave1 = math.sin(dist1 * 0.05) * math.exp(-dist1 * 0.002)
            wave2 = math.sin(dist2 * 0.05) * math.exp(-dist2 * 0.002)
            
            # 干涉强度
            intensity = abs(wave1 + wave2)
            
            if intensity > 0.1:  # 只绘制有强度的区域
                # 根据强度设置颜色
                r = intensity * 0.3
                g = intensity * 0.8
                b = intensity * 1.0
                alpha = min(0.8, intensity * 2)
                
                ctx.set_source_rgba(r, g, b, alpha)
                ctx.rectangle(x, y, 4, 4)
                ctx.fill()
    
    # 标记波源
    for sx, sy in [(source1_x, source1_y), (source2_x, source2_y)]:
        source_gradient = cairo.RadialGradient(sx, sy, 0, sx, sy, 20)
        source_gradient.add_color_stop_rgba(0, 1, 1, 1, 1)
        source_gradient.add_color_stop_rgba(1, 1, 1, 1, 0)
        
        ctx.set_source(source_gradient)
        ctx.arc(sx, sy, 20, 0, 2 * math.pi)
        ctx.fill()
    
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


def create_fractal_tree():
    """创建分形树图案"""
    width, height = 800, 800
    surface = cairo.ImageSurface(cairo.FORMAT_ARGB32, width, height)
    ctx = cairo.Context(surface)
    
    # 天空渐变背景
    bg_gradient = cairo.LinearGradient(0, 0, 0, height)
    bg_gradient.add_color_stop_rgba(0, 0.5, 0.7, 1, 1)    # 天空蓝
    bg_gradient.add_color_stop_rgba(0.7, 0.9, 0.9, 1, 1)  # 浅蓝
    bg_gradient.add_color_stop_rgba(1, 0.2, 0.8, 0.3, 1)  # 草地绿
    
    ctx.set_source(bg_gradient)
    ctx.rectangle(0, 0, width, height)
    ctx.fill()
    
    def draw_branch(start_x, start_y, length, angle, depth):
        if depth <= 0 or length < 5:
            return
        
        # 计算终点
        end_x = start_x + length * math.cos(angle)
        end_y = start_y + length * math.sin(angle)
        
        # 树枝颜色（从棕色到绿色）
        color_factor = max(0, 1 - depth / 8)
        r = 0.4 + color_factor * 0.2
        g = 0.2 + (1 - color_factor) * 0.6
        b = 0.1 + (1 - color_factor) * 0.2
        
        # 线条粗细随深度变化
        line_width = max(1, depth * 1.5)
        
        ctx.set_source_rgba(r, g, b, 0.8)
        ctx.set_line_width(line_width)
        ctx.move_to(start_x, start_y)
        ctx.line_to(end_x, end_y)
        ctx.stroke()
        
        # 递归绘制子分支
        new_length = length * 0.7
        draw_branch(end_x, end_y, new_length, angle - math.pi/6, depth - 1)
        draw_branch(end_x, end_y, new_length, angle + math.pi/6, depth - 1)
        draw_branch(end_x, end_y, new_length * 0.8, angle, depth - 1)
    
    # 从底部中心开始绘制主干
    start_x = width // 2
    start_y = height - 50
    initial_length = 120
    initial_angle = -math.pi / 2  # 向上
    
    draw_branch(start_x, start_y, initial_length, initial_angle, 8)
    
    buf = surface.get_data()
    img_array = np.ndarray(shape=(height, width, 4), dtype=np.uint8, buffer=buf)
    img_array = img_array[:, :, [2, 1, 0, 3]]
    return Image.fromarray(img_array, 'RGBA')


if __name__ == "__main__":
    print("正在生成艺术图案...")
    
    # 创建万花筒图案
    print("1. 万花筒图案")
    kaleidoscope = create_kaleidoscope_pattern()
    kaleidoscope.save("art_kaleidoscope.png")
    
    # 创建螺旋星系
    print("2. 螺旋星系")
    galaxy = create_spiral_galaxy()
    galaxy.save("art_spiral_galaxy.png")
    
    # 创建花卉曼陀罗
    print("3. 花卉曼陀罗")
    mandala = create_flower_mandala()
    mandala.save("art_flower_mandala.png")
    
    # 创建波浪干涉
    print("4. 波浪干涉图案")
    waves = create_wave_interference()
    waves.save("art_wave_interference.png")
    
    # 创建分形树
    print("5. 分形树")
    tree = create_fractal_tree()
    tree.save("art_fractal_tree.png")
    
    print("所有艺术图案已生成完成！")
    
    # 显示其中一个图案
    mandala.show()
