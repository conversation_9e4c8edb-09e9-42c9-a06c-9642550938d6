import matplotlib.pyplot as plt
import numpy as np

def visualize_matrix():
    """
    Creates and visualizes a 25x25 point matrix, connecting points
    with various mathematical formulas.
    """
    size = 25
    x = np.arange(0, size)
    y = np.arange(0, size)
    xx, yy = np.meshgrid(x, y)

    fig, ax = plt.subplots(figsize=(10, 10))

    # 1. Plot all the points in the 25x25 matrix
    ax.scatter(xx, yy, color='lightgray', s=20, zorder=1)

    # 2. Connect points using mathematical formulas

    # Formula 1: Main diagonal (y = x)
    ax.plot(x, x, color='blue', label='y = x')

    # Formula 2: Anti-diagonal (y = 24 - x)
    ax.plot(x, size - 2 - x, color='green', label=f'y = {size-1} - x')

    # Formula 3: A parabola
    x_parabola = np.linspace(0, size - 1, 100)
    y_parabola = ((x_parabola - (size-1)/2)**2) / 10 + 2
    ax.plot(x_parabola, y_parabola, color='red', label='y = (x-12)²/10 + 2')

    # Formula 4: A sine wave
    x_sine = np.linspace(0, size - 1, 100)
    y_sine = (size-1)/2 + 10 * np.sin(x_sine / 4)
    ax.plot(x_sine, y_sine, color='purple', label='y = 12 + 10*sin(x/4)')

    # --- Plot styling ---
    ax.set_title('25x25 Point Matrix with Connections')
    ax.set_xlabel('X-axis')
    ax.set_ylabel('Y-axis')
    ax.set_xticks(x)
    ax.set_yticks(y)
    ax.set_xlim(-1, size)
    ax.set_ylim(-1, size)
    ax.set_aspect('equal', adjustable='box')
    ax.grid(True, which='both', linestyle='--', linewidth=0.5)
    ax.legend()
    
    # Invert y-axis to have (0,0) at the top-left corner like a matrix
    ax.invert_yaxis()

    # Save the plot to a file
    plt.savefig('connected_matrix.png')
    print("Plot saved to connected_matrix.png")
    plt.show()

if __name__ == '__main__':
    visualize_matrix()
