import skia
import numpy as np
from PIL import Image
import io


def create_black_hole_with_skia():
    """使用Skia-Python创建黑洞视觉效果"""
    width, height = 800, 600
    
    # 创建画布
    surface = skia.Surface(width, height)
    canvas = surface.getCanvas()
    
    # 清空画布为深空背景
    canvas.clear(skia.Color(5, 5, 20))  # 深蓝色太空背景
    
    # 创建吸积盘渐变效果
    center_x, center_y = width // 2, height // 2
    
    # 外层吸积盘 - 橙红色渐变
    outer_colors = [
        skia.Color(255, 100, 0, 200),   # 橙色
        skia.Color(255, 50, 0, 150),    # 红橙色
        skia.Color(100, 0, 0, 80),      # 暗红色
        skia.Color(0, 0, 0, 0)          # 透明
    ]
    outer_positions = [0.0, 0.3, 0.7, 1.0]
    outer_gradient = skia.GradientShader.MakeRadial(
        (center_x, center_y), 250,
        outer_colors, outer_positions
    )
    
    paint = skia.Paint()
    paint.setShader(outer_gradient)
    paint.setBlendMode(skia.BlendMode.kScreen)  # 发光混合模式
    canvas.drawCircle(center_x, center_y, 250, paint)
    
    # 内层吸积盘 - 白热化效果
    inner_colors = [
        skia.Color(255, 255, 255, 255), # 白色
        skia.Color(255, 200, 100, 200), # 淡黄色
        skia.Color(255, 100, 0, 100),   # 橙色
        skia.Color(0, 0, 0, 0)          # 透明
    ]
    inner_positions = [0.0, 0.4, 0.8, 1.0]
    inner_gradient = skia.GradientShader.MakeRadial(
        (center_x, center_y), 120,
        inner_colors, inner_positions
    )
    
    paint.setShader(inner_gradient)
    canvas.drawCircle(center_x, center_y, 120, paint)
    
    # 事件视界 - 纯黑圆形
    black_paint = skia.Paint()
    black_paint.setColor(skia.Color(0, 0, 0, 255))
    canvas.drawCircle(center_x, center_y, 45, black_paint)
    
    # 添加引力透镜效果 - 光线弯曲
    num_rays = 16  # 光线数量
    for i in range(num_rays):
        angle = i * 2 * np.pi / num_rays  # 360度均匀分布
        start_x = center_x + 300 * np.cos(angle)
        start_y = center_y + 300 * np.sin(angle)
        
        # 创建弯曲路径
        path = skia.Path()
        path.moveTo(start_x, start_y)
        
        # 使用二次贝塞尔曲线模拟光线弯曲
        control_x = center_x + 150 * np.cos(angle + np.pi/6)
        control_y = center_y + 150 * np.sin(angle + np.pi/6)
        end_x = center_x + 80 * np.cos(angle + np.pi/3)
        end_y = center_y + 80 * np.sin(angle + np.pi/3)
        
        path.quadTo(control_x, control_y, end_x, end_y)
        
        line_paint = skia.Paint()
        line_paint.setColor(skia.Color(100, 150, 255, 150))  # 蓝白色光线
        line_paint.setStrokeWidth(3)
        line_paint.setStyle(skia.Paint.kStroke_Style)
        canvas.drawPath(path, line_paint)
    
    # 添加背景星星
    np.random.seed(42)  # 固定随机种子
    star_paint = skia.Paint()
    star_paint.setStyle(skia.Paint.kFill_Style)
    
    for _ in range(200):
        star_x = np.random.randint(0, width)
        star_y = np.random.randint(0, height)
        star_radius = np.random.uniform(1, 3)
        star_alpha = np.random.randint(100, 255)
        
        # 避免在黑洞区域绘制星星
        if np.sqrt((star_x - center_x)**2 + (star_y - center_y)**2) > 280:
            star_paint.setColor(skia.Color(255, 255, 255, star_alpha))
            canvas.drawCircle(star_x, star_y, star_radius, star_paint)
    
    # 转换为PIL图像
    image = surface.makeImageSnapshot()
    png_data = image.encodeToData()
    pil_image = Image.open(io.BytesIO(png_data.data()))
    
    return pil_image


# 创建并直接展示黑洞图像
black_hole_image = create_black_hole_with_skia()
black_hole_image.show()